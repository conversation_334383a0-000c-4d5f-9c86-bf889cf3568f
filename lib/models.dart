// lib/models.dart
// This file contains data models used throughout the application

class AreaCodeDto {
  final String areaCode;
  final String? areaName;
  final String? areaType;

  AreaCodeDto({
    required this.areaCode,
    this.areaName,
    this.areaType,
  });

  factory AreaCodeDto.fromJson(Map<String, dynamic> json) {
    return AreaCodeDto(
      areaCode: json['areaCode'] ?? '',
      areaName: json['areaName'],
      areaType: json['areaType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'areaCode': areaCode,
      'areaName': areaName,
      'areaType': areaType,
    };
  }

  @override
  String toString() {
    return '$areaCode - ${areaName ?? 'Unknown Area'}';
  }
}

class RetailerInfoDto {
  final String? code;
  final String? name;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? kycStatus;
  final String? contactPerson;
  final String? contactNumber;

  RetailerInfoDto({
    this.code,
    this.name,
    this.address,
    this.latitude,
    this.longitude,
    this.kycStatus,
    this.contactPerson,
    this.contactNumber,
  });

  factory RetailerInfoDto.fromJson(Map<String, dynamic> json) {
    return RetailerInfoDto(
      code: json['code'],
      name: json['name'],
      address: json['address'],
      latitude: json['latitude'] != null ? double.tryParse(json['latitude'].toString()) : null,
      longitude: json['longitude'] != null ? double.tryParse(json['longitude'].toString()) : null,
      kycStatus: json['kycStatus'],
      contactPerson: json['contactPerson'],
      contactNumber: json['contactNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'kycStatus': kycStatus,
      'contactPerson': contactPerson,
      'contactNumber': contactNumber,
    };
  }

  @override
  String toString() {
    return '$code - $name';
  }
}

class DsrHeaderSubmitDto {
  final String? loginIdM;
  final String? docuDate;
  final String? dsrParam;
  final String? cusRtlFl;
  final String? cusRtlCd;
  final String? areaCode;
  final String? cuRtType;
  final String? geoLatit;
  final String? geoLongt;
  final double? distance;
  final String? distanceExceptionReason;

  DsrHeaderSubmitDto({
    this.loginIdM,
    this.docuDate,
    this.dsrParam,
    this.cusRtlFl,
    this.cusRtlCd,
    this.areaCode,
    this.cuRtType,
    this.geoLatit,
    this.geoLongt,
    this.distance,
    this.distanceExceptionReason,
  });

  Map<String, dynamic> toJson() {
    return {
      'loginIdM': loginIdM,
      'docuDate': docuDate,
      'dsrParam': dsrParam,
      'cusRtlFl': cusRtlFl,
      'cusRtlCd': cusRtlCd,
      'areaCode': areaCode,
      'cuRtType': cuRtType,
      'geoLatit': geoLatit,
      'geoLongt': geoLongt,
      'distance': distance,
      'distanceExceptionReason': distanceExceptionReason,
    };
  }
}

class WorkFromHomeDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  WorkFromHomeDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}

class AnyOtherActivityDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String actdetl1;
  final String actdetl2;
  final String actdetl3;
  final String othrnote;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  AnyOtherActivityDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.actdetl1,
    required this.actdetl2,
    required this.actdetl3,
    required this.othrnote,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'actdetl1': actdetl1,
      'actdetl2': actdetl2,
      'actdetl3': actdetl3,
      'othrnote': othrnote,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}

class DsrActivityFullDto {
  final String? loginIdM;
  final String proctype;
  final String? docuDate;
  final String? dsrParam;
  final String? otherRemarks;
  final String? dsrRem01;
  final String? dsrRem02;
  final String? dsrRem03;
  final List<DsrOrderItemDto>? orderItems;
  final List<DsrGiftItemDto>? giftItems;
  final List<DsrCompetitorAvgSaleDto>? competitorSales;
  final List<DsrMarketWcpSkuDto>? marketWcpSkus;

  // Additional properties for DsrVisit
  final String? docuNumb;
  final String? cusRtlFl;
  final String? cusRtlCd;
  final String? areaCode;
  final String? cusRtlNm;
  final String? kycStatus;
  final String? marketName;
  final String? displayContestParticipation;
  final String? pendingIssueStatus;
  final String? pendingIssueDetailType;
  final String? pendingIssueSpecifyText;
  final String? enrolWCSlab;
  final String? enrolWCPSlab;
  final String? enrolVAPSlab;
  final String? bwStockWC;
  final String? bwStockWCP;
  final String? bwStockVAP;
  final String? brandsSellingWC;
  final String? wcIndustryVolume;
  final String? brandsSellingWCP;
  final String? wcpIndustryVolume;
  final String? last3MonthsBwWC;
  final String? last3MonthsBwWCP;
  final String? last3MonthsBwVAP;
  final String? currentMonthBwWC;
  final String? currentMonthBwWCP;
  final String? currentMonthBwVAP;
  final String? orderExecDate;
  final String? isTileAdhesiveSeller;
  final String? tileAdhesiveStock;
  final String? geoLatit;
  final String? geoLongt;
  final String? purchaserLatit;
  final String? purchaserLongt;
  final String? locationCapturedAddress;
  final String? distanceExceptionReason;

  // Getter for compatibility
  List<DsrCompetitorAvgSaleDto>? get competitorAvgSales => competitorSales;

  DsrActivityFullDto({
    this.loginIdM,
    required this.proctype,
    this.docuDate,
    this.dsrParam,
    this.otherRemarks,
    this.dsrRem01,
    this.dsrRem02,
    this.dsrRem03,
    this.orderItems,
    this.giftItems,
    this.competitorSales,
    this.marketWcpSkus,
    this.docuNumb,
    this.cusRtlFl,
    this.cusRtlCd,
    this.areaCode,
    this.cusRtlNm,
    this.kycStatus,
    this.marketName,
    this.displayContestParticipation,
    this.pendingIssueStatus,
    this.pendingIssueDetailType,
    this.pendingIssueSpecifyText,
    this.enrolWCSlab,
    this.enrolWCPSlab,
    this.enrolVAPSlab,
    this.bwStockWC,
    this.bwStockWCP,
    this.bwStockVAP,
    this.brandsSellingWC,
    this.wcIndustryVolume,
    this.brandsSellingWCP,
    this.wcpIndustryVolume,
    this.last3MonthsBwWC,
    this.last3MonthsBwWCP,
    this.last3MonthsBwVAP,
    this.currentMonthBwWC,
    this.currentMonthBwWCP,
    this.currentMonthBwVAP,
    this.orderExecDate,
    this.isTileAdhesiveSeller,
    this.tileAdhesiveStock,
    this.geoLatit,
    this.geoLongt,
    this.purchaserLatit,
    this.purchaserLongt,
    this.locationCapturedAddress,
    this.distanceExceptionReason,
  });

  Map<String, dynamic> toJson() {
    return {
      'loginIdM': loginIdM,
      'proctype': proctype,
      'docuDate': docuDate,
      'dsrParam': dsrParam,
      'otherRemarks': otherRemarks,
      'dsrRem01': dsrRem01,
      'dsrRem02': dsrRem02,
      'dsrRem03': dsrRem03,
      'orderItems': orderItems?.map((item) => item.toJson()).toList(),
      'giftItems': giftItems?.map((item) => item.toJson()).toList(),
      'competitorSales': competitorSales?.map((item) => item.toJson()).toList(),
      'marketWcpSkus': marketWcpSkus?.map((item) => item.toJson()).toList(),
      'docuNumb': docuNumb,
      'cusRtlFl': cusRtlFl,
      'cusRtlCd': cusRtlCd,
      'areaCode': areaCode,
      'cusRtlNm': cusRtlNm,
      'kycStatus': kycStatus,
      'marketName': marketName,
      'displayContestParticipation': displayContestParticipation,
      'pendingIssueStatus': pendingIssueStatus,
      'pendingIssueDetailType': pendingIssueDetailType,
      'pendingIssueSpecifyText': pendingIssueSpecifyText,
      'enrolWCSlab': enrolWCSlab,
      'enrolWCPSlab': enrolWCPSlab,
      'enrolVAPSlab': enrolVAPSlab,
      'bwStockWC': bwStockWC,
      'bwStockWCP': bwStockWCP,
      'bwStockVAP': bwStockVAP,
      'brandsSellingWC': brandsSellingWC,
      'wcIndustryVolume': wcIndustryVolume,
      'brandsSellingWCP': brandsSellingWCP,
      'wcpIndustryVolume': wcpIndustryVolume,
      'last3MonthsBwWC': last3MonthsBwWC,
      'last3MonthsBwWCP': last3MonthsBwWCP,
      'last3MonthsBwVAP': last3MonthsBwVAP,
      'currentMonthBwWC': currentMonthBwWC,
      'currentMonthBwWCP': currentMonthBwWCP,
      'currentMonthBwVAP': currentMonthBwVAP,
      'orderExecDate': orderExecDate,
      'isTileAdhesiveSeller': isTileAdhesiveSeller,
      'tileAdhesiveStock': tileAdhesiveStock,
      'geoLatit': geoLatit,
      'geoLongt': geoLongt,
      'purchaserLatit': purchaserLatit,
      'purchaserLongt': purchaserLongt,
      'locationCapturedAddress': locationCapturedAddress,
      'distanceExceptionReason': distanceExceptionReason,
    };
  }
}

class DsrOrderItemDto {
  final String? prodCode;
  final int? quantityInBags;
  final double? quantityInMT;
  final String? wcQty;
  final String? wcpQty;
  final String? repoCatg;

  DsrOrderItemDto({
    this.prodCode,
    this.quantityInBags,
    this.quantityInMT,
    this.wcQty,
    this.wcpQty,
    this.repoCatg,
  });

  Map<String, dynamic> toJson() {
    return {
      'prodCode': prodCode,
      'quantityInBags': quantityInBags,
      'quantityInMT': quantityInMT,
      'wcQty': wcQty,
      'wcpQty': wcpQty,
      'repoCatg': repoCatg,
    };
  }
}

class DsrGiftItemDto {
  final String? giftTypeCode;
  final int? quantity;
  final String? brandName;
  final String? prdCodMk;

  DsrGiftItemDto({
    this.giftTypeCode,
    this.quantity,
    this.brandName,
    this.prdCodMk,
  });

  Map<String, dynamic> toJson() {
    return {
      'giftTypeCode': giftTypeCode,
      'quantity': quantity,
      'brandName': brandName,
      'prdCodMk': prdCodMk,
    };
  }
}

class DsrCompetitorAvgSaleDto {
  final String? competitorName;
  final double? avgSale;
  final String? wcQty;
  final String? wcpQty;

  DsrCompetitorAvgSaleDto({
    this.competitorName,
    this.avgSale,
    this.wcQty,
    this.wcpQty,
  });

  Map<String, dynamic> toJson() {
    return {
      'competitorName': competitorName,
      'avgSale': avgSale,
      'wcQty': wcQty,
      'wcpQty': wcpQty,
    };
  }
}

class DsrMarketWcpSkuDto {
  final String? skuCode;
  final String? wcQty;
  final String? wcpQty;
  final String? brandName;
  final String? prdCodMk;
  final String? bPriceVl;
  final String? cPriceVl;

  DsrMarketWcpSkuDto({
    this.skuCode,
    this.wcQty,
    this.wcpQty,
    this.brandName,
    this.prdCodMk,
    this.bPriceVl,
    this.cPriceVl,
  });

  Map<String, dynamic> toJson() {
    return {
      'skuCode': skuCode,
      'wcQty': wcQty,
      'wcpQty': wcpQty,
      'brandName': brandName,
      'prdCodMk': prdCodMk,
      'bPriceVl': bPriceVl,
      'cPriceVl': cPriceVl,
    };
  }
}

class BtlActivityDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String actdetl1;
  final String actdetl2;
  final String actdetl3;
  final String othrnote;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  BtlActivityDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.actdetl1,
    required this.actdetl2,
    required this.actdetl3,
    required this.othrnote,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'actdetl1': actdetl1,
      'actdetl2': actdetl2,
      'actdetl3': actdetl3,
      'othrnote': othrnote,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}

class CheckSamplingDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String actdetl1;
  final String actdetl2;
  final String actdetl3;
  final String othrnote;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  CheckSamplingDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.actdetl1,
    required this.actdetl2,
    required this.actdetl3,
    required this.othrnote,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'actdetl1': actdetl1,
      'actdetl2': actdetl2,
      'actdetl3': actdetl3,
      'othrnote': othrnote,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}

class MeetingNewPurchaserDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String actdetl1;
  final String actdetl2;
  final String actdetl3;
  final String othrnote;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  MeetingNewPurchaserDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.actdetl1,
    required this.actdetl2,
    required this.actdetl3,
    required this.othrnote,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'actdetl1': actdetl1,
      'actdetl2': actdetl2,
      'actdetl3': actdetl3,
      'othrnote': othrnote,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}

class MeetingContractorDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String actdetl1;
  final String actdetl2;
  final String actdetl3;
  final String othrnote;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  MeetingContractorDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.actdetl1,
    required this.actdetl2,
    required this.actdetl3,
    required this.othrnote,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'actdetl1': actdetl1,
      'actdetl2': actdetl2,
      'actdetl3': actdetl3,
      'othrnote': othrnote,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}