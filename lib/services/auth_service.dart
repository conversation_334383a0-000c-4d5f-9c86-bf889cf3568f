import 'dart:convert';
import 'dart:io';
import 'package:http/io_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Authentication result enum
enum AuthResult {
  success,
  blocked,
  invalidCredentials,
  networkError,
  serverError,
  unknownError
}

/// Authentication response model
class AuthResponse {
  final AuthResult result;
  final String? message;
  final String? role;
  final Map<String, dynamic>? userData;
  final String? token;

  AuthResponse({
    required this.result,
    this.message,
    this.role,
    this.userData,
    this.token,
  });
}

class AuthService {
  static const String _apiUrl = "https://**************:7023/api/Auth/login";

  /// Authenticate user with blocking check
  static Future<AuthResponse> authenticateUser({
    required String userID,
    required String password,
    required String appRegId,
  }) async {
    try {
      // Check internet connectivity
      final result = await InternetAddress.lookup('google.com');
      if (result.isEmpty || result.first.rawAddress.isEmpty) {
        return AuthResponse(
          result: AuthResult.networkError,
          message: 'No Internet Connection\nPlease check your connection and try again.',
        );
      }

      // Prepare request
      final Map<String, dynamic> requestBody = {
        'userID': userID,
        'password': password,
        'appRegId': appRegId,
      };

      final client = IOClient(
        HttpClient()..badCertificateCallback = (cert, host, port) => true,
      );

      final response = await client.post(
        Uri.parse(_apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      client.close();

      // Handle different response scenarios
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['result'] == 'success') {
          // Successful authentication
          return AuthResponse(
            result: AuthResult.success,
            role: responseData['role'] ?? '',
            userData: responseData,
            token: responseData['token'], // JWT token from backend
            message: responseData['message'] ?? 'Authentication successful',
          );
        } else {
          return AuthResponse(
            result: AuthResult.invalidCredentials,
            message: responseData['message'] ?? 'Authentication failed. Please check your credentials.',
          );
        }
      } else if (response.statusCode == 403) {
        // Handle HTTP 403 Forbidden for blocked users
        final Map<String, dynamic>? responseData =
            response.body.isNotEmpty ? jsonDecode(response.body) : null;

        return AuthResponse(
          result: AuthResult.blocked,
          message: responseData?['message'] ?? 'Your account has been blocked.',
        );
      } else if (response.statusCode == 400) {
        // Handle bad request (invalid credentials)
        final Map<String, dynamic>? responseData =
            response.body.isNotEmpty ? jsonDecode(response.body) : null;

        return AuthResponse(
          result: AuthResult.invalidCredentials,
          message: responseData?['message'] ?? 'Invalid credentials.',
        );
      } else {
        return AuthResponse(
          result: AuthResult.serverError,
          message: 'Failed to authenticate. Please try again.',
        );
      }
    } on SocketException catch (_) {
      return AuthResponse(
        result: AuthResult.networkError,
        message: 'No Internet Connection\nPlease check your connection and try again.',
      );
    } catch (e) {
      return AuthResponse(
        result: AuthResult.unknownError,
        message: 'An error occurred: ${e.toString()}',
      );
    }
  }

  /// Save authentication data to local storage
  static Future<void> saveAuthData({
    required String role,
    String? token,
    Map<String, dynamic>? userData,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLoggedIn', true);

    if (role.isNotEmpty) {
      await prefs.setString('userRole', role);
    }

    if (token != null) {
      await prefs.setString('authToken', token);
    }

    if (userData != null) {
      await prefs.setString('userData', jsonEncode(userData));
    }
  }

  /// Clear authentication data from local storage
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLoggedIn', false);
    await prefs.remove('userRole');
    await prefs.remove('authToken');
    await prefs.remove('userData');
  }

  /// Check if user is currently logged in
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('isLoggedIn') ?? false;
  }

  /// Get stored user role
  static Future<String?> getUserRole() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('userRole');
  }

  /// Get stored auth token
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('authToken');
  }

  /// Get stored user data
  static Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString('userData');
    if (userDataString != null) {
      return jsonDecode(userDataString);
    }
    return null;
  }
}
