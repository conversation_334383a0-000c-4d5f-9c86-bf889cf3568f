// lib/widgets/blocked_user_dialog.dart

import 'package:flutter/material.dart';

class BlockedUserDialog {
  /// Shows a modal dialog with [message], plus a “supportEmail” line if provided.
  static Future<void> show(
      BuildContext context, {
        required String message,
        required String supportEmail,
      }) {
    return showDialog(
      context: context,
      barrierDismissible: false, // user must tap "OK"
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        title: const Text(
          'Account Blocked',
          style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
        ),
        content: Text(
          '$message\n\nIf you believe this is an error, contact:\n$supportEmail',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'OK',
              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
