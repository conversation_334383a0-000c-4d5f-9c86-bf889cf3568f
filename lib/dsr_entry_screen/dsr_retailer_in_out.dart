// lib/dsr_entry_screen/dsr_retailer_in_out.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:dropdown_search/dropdown_search.dart';

import '../theme/app_theme.dart';
import '../models.dart';
import '../api_service.dart';
import 'dsr_entry.dart';

class DsrRetailerInOut extends StatefulWidget {
  const DsrRetailerInOut({Key? key}) : super(key: key);

  @override
  State<DsrRetailerInOut> createState() => _DsrRetailerInOutState();
}

class _DsrRetailerInOutState extends State<DsrRetailerInOut> {
  final ApiService _apiService = ApiService();
  final _formKey = GlobalKey<FormState>();

  String? _processType = 'Select';
  final List<String> _processOptions = ['Select', 'Add', 'Update'];

  DateTime? _selectedSubmissionDate;
  DateTime? _selectedReportDate;
  final TextEditingController _submissionDateController = TextEditingController();
  final TextEditingController _reportDateController = TextEditingController();

  AreaCodeDto? _selectedAreaCode;
  RetailerInfoDto? _selectedRetailer;

  String? _geoLat;
  String? _geoLong;

  @override
  void initState() {
    super.initState();
    _selectedSubmissionDate = DateTime.now();
    _submissionDateController.text =
        DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
    _selectedReportDate = DateTime.now();
    _reportDateController.text =
        DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
  }

  @override
  void dispose() {
    _submissionDateController.dispose();
    _reportDateController.dispose();
    super.dispose();
  }

  Future<List<AreaCodeDto>> _fetchAreaCodes(String? filter) async {
    try {
      return await _apiService.getAreaCodes(search: filter);
    } catch (e) {
      return [];
    }
  }

  Future<List<RetailerInfoDto>> _fetchRetailers(String? filter) async {
    if (_selectedAreaCode == null) return [];
    try {
      return await _apiService.getRetailers(
        areaCode: _selectedAreaCode!.areaCode,
        cusRtlFl: 'R',
        search: filter,
      );
    } catch (e) {
      return [];
    }
  }

  Future<void> _pickDate(
      BuildContext context,
      TextEditingController controller,
      bool isSubmissionDate,
      ) async {
    final now = DateTime.now();
    DateTime initial = isSubmissionDate
        ? (_selectedSubmissionDate ?? now)
        : (_selectedReportDate ?? now);

    final picked = await showDatePicker(
      context: context,
      initialDate: initial,
      firstDate: DateTime(2000),
      lastDate: DateTime(now.year + 5),
      builder: (ctx, child) => AppTheme.datePickerTheme(ctx, child!),
    );
    if (picked != null) {
      setState(() {
        final formatted = DateFormat('yyyy-MM-dd').format(picked);
        if (isSubmissionDate) {
          _selectedSubmissionDate = picked;
          controller.text = formatted;
        } else {
          _selectedReportDate = picked;
          controller.text = formatted;
        }
      });
    }
  }

  Future<void> _submitForm({bool exitAfter = false}) async {
    if (!_formKey.currentState!.validate()) return;
    // Construct DTO
    final dto = DsrRetailerInOutDto(
      proctype: _processType == 'Select' ? 'Add' : _processType!,
      submdate: _submissionDateController.text,
      repodate: _reportDateController.text,
      areaCode: _selectedAreaCode?.areaCode,
      retailerCode: _selectedRetailer?.code,
      retailerName: _selectedRetailer?.name,
      geoLatit: _geoLat,
      geoLongt: _geoLong,
    );

    try {
      await _apiService.submitRetailerInOut(dto, "/dsractivity/retailerinout");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Retailer In/Out submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        if (exitAfter) {
          Navigator.of(context).pop();
        } else {
          _resetForm();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Submission failed: $e'),
            backgroundColor: Colors.redAccent,
          ),
        );
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    setState(() {
      _processType = 'Select';
      _selectedSubmissionDate = DateTime.now();
      _submissionDateController.text =
          DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
      _selectedReportDate = DateTime.now();
      _reportDateController.text =
          DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
      _selectedAreaCode = null;
      _selectedRetailer = null;
      _geoLat = null;
      _geoLong = null;
    });
  }

  Widget _buildGeoField(String label, String? value, ValueChanged<String> onChanged) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        initialValue: value,
        readOnly: false,
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        decoration: AppTheme.textFieldInputDecoration(
          hintText: label,
          labelText: label,
        ),
        onChanged: onChanged,
        validator: (val) {
          if (val == null || val.trim().isEmpty) {
            return '$label is required';
          }
          return null;
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => const DsrEntry()),
            );
          },
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        ),
        title: const Text('DSR Retailer In/Out'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // Process Section
              AppTheme.buildSectionCard(
                title: 'Process',
                icon: Icons.settings_outlined,
                children: [
                  AppTheme.buildLabel('Process Type'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _processType,
                    decoration:
                    AppTheme.dropdownDecoration(hintText: 'Select Process'),
                    items: _processOptions
                        .map((item) => DropdownMenuItem(
                      value: item,
                      child: Text(item),
                    ))
                        .toList(),
                    onChanged: (val) => setState(() => _processType = val),
                    validator: (val) =>
                    (val == null || val == 'Select') ? 'Required' : null,
                  ),
                ],
              ),

              // Date Information Section
              AppTheme.buildSectionCard(
                title: 'Date Information',
                icon: Icons.date_range_outlined,
                children: [
                  AppTheme.buildLabel('Submission Date'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(
                    context,
                    _submissionDateController,
                        () => _pickDate(true),
                    'Select Submission Date',
                    initialDate: _selectedSubmissionDate,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Report Date'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(
                    context,
                    _reportDateController,
                        () => _pickDate(false),
                    'Select Report Date',
                    initialDate: _selectedReportDate,
                  ),
                ],
              ),

              // Area Code & Retailer Section
              AppTheme.buildSectionCard(
                title: 'Area & Retailer',
                icon: Icons.store_mall_directory_outlined,
                children: [
                  AppTheme.buildLabel('Area Code'),
                  const SizedBox(height: 8),
                  DropdownSearch<AreaCodeDto>(
                    asyncItems: _fetchAreaCodes,
                    selectedItem: _selectedAreaCode,
                    onChanged: (val) {
                      setState(() {
                        _selectedAreaCode = val;
                        _selectedRetailer = null;
                      });
                    },
                    validator: (val) =>
                    (val == null) ? 'Please select an area code' : null,
                    popupProps: PopupProps.menu(
                      showSearchBox: true,
                      searchFieldProps: TextFieldProps(
                        decoration: AppTheme.textFieldInputDecoration(
                            hintText: 'Search Area Code'),
                      ),
                      itemBuilder: (ctx, item, isSelected) => ListTile(
                        title: Text(item.toString()),
                      ),
                      emptyBuilder: (ctx, search) => const Center(
                        child: Padding(
                          padding: EdgeInsets.all(12.0),
                          child: Text('No areas found. Type to search.'),
                        ),
                      ),
                    ),
                    dropdownDecoratorProps: DropDownDecoratorProps(
                      dropdownSearchDecoration: AppTheme.dropdownDecoration(
                        hintText: 'Select Area Code',
                      ),
                    ),
                    compareFn: (a, b) => a?.areaCode == b?.areaCode,
                    itemAsString: (dto) => dto == null ? '' : dto.toString(),
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Retailer'),
                  const SizedBox(height: 8),
                  DropdownSearch<RetailerInfoDto>(
                    asyncItems: _fetchRetailers,
                    selectedItem: _selectedRetailer,
                    onChanged: (val) => setState(() => _selectedRetailer = val),
                    validator: (val) =>
                    (val == null) ? 'Please select a retailer' : null,
                    popupProps: PopupProps.menu(
                      showSearchBox: true,
                      searchFieldProps: TextFieldProps(
                        decoration: AppTheme.textFieldInputDecoration(
                            hintText: 'Search Retailer'),
                      ),
                      itemBuilder: (ctx, item, isSelected) => ListTile(
                        title: Text(item.name ?? ''),
                        subtitle: Text(item.code ?? ''),
                      ),
                      emptyBuilder: (ctx, search) => const Center(
                        child: Padding(
                          padding: EdgeInsets.all(12.0),
                          child: Text('No retailers found.'),
                        ),
                      ),
                    ),
                    dropdownDecoratorProps: DropDownDecoratorProps(
                      dropdownSearchDecoration: AppTheme.dropdownDecoration(
                        hintText: 'Select Retailer',
                      ),
                    ),
                    compareFn: (a, b) => a?.code == b?.code,
                    itemAsString: (dto) =>
                    dto == null ? '' : '${dto.code} - ${dto.name}',
                  ),
                ],
              ),

              // Geolocation Section
              AppTheme.buildSectionCard(
                title: 'Geo Coordinates',
                icon: Icons.location_on_outlined,
                children: [
                  AppTheme.buildLabel('Latitude'),
                  _buildGeoField(
                    'Latitude',
                    _geoLat,
                        (val) => _geoLat = val,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Longitude'),
                  _buildGeoField(
                    'Longitude',
                    _geoLong,
                        (val) => _geoLong = val,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Submit Section
              Container(
                padding: const EdgeInsets.all(20),
                decoration: AppTheme.cardDecoration,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.save_alt_rounded,
                            color: AppTheme.primaryColor, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          'Submit Retailer In/Out',
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(color: AppTheme.primaryColor),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      onPressed: _isSubmitting
                          ? null
                          : () => _submitForm(exitAfter: false),
                      icon: const Icon(Icons.save_outlined),
                      label: _isSubmitting
                          ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                            color: Colors.white, strokeWidth: 3),
                      )
                          : const Text('Submit & New'),
                      style: AppTheme.elevatedButtonStyle(),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _isSubmitting
                          ? null
                          : () => _submitForm(exitAfter: true),
                      icon: const Icon(Icons.check_circle_outline),
                      label: _isSubmitting
                          ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                            color: Colors.white, strokeWidth: 3),
                      )
                          : const Text('Submit & Exit'),
                      style: AppTheme.elevatedButtonStyle(
                          bgColor: AppTheme.successColor),
                    ),
                    const SizedBox(height: 12),
                    OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(builder: (_) => const DsrEntry()),
                        );
                      },
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Back to DSR Entry'),
                      style: AppTheme.outlinedButtonStyle(),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}

// --- Add the DTO class below, matching your models.dart ---

// Example DTO; adjust fields/names to match your backend expectations.
class DsrRetailerInOutDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String? areaCode;
  final String? retailerCode;
  final String? retailerName;
  final String? geoLatit;
  final String? geoLongt;

  DsrRetailerInOutDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    this.areaCode,
    this.retailerCode,
    this.retailerName,
    this.geoLatit,
    this.geoLongt,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'areaCode': areaCode,
      'retailerCode': retailerCode,
      'retailerName': retailerName,
      'geoLatit': geoLatit,
      'geoLongt': geoLongt,
    };
  }
}
