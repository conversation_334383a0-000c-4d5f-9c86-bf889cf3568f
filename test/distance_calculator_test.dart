import 'package:flutter_test/flutter_test.dart';
import 'package:learning2/utils/distance_calculator.dart';

void main() {
  group('DistanceCalculator Tests', () {
    test('should calculate distance between two points correctly', () {
      // Test coordinates: Mumbai to Delhi (approximately 1150 km)
      double mumbaiLat = 19.0760;
      double mumbaiLon = 72.8777;
      double delhiLat = 28.7041;
      double delhiLon = 77.1025;

      double distance = DistanceCalculator.calculateDistance(
        mumbaiLat,
        mumbaiLon,
        delhiLat,
        delhiLon,
      );

      // Distance should be approximately 1150 km (1,150,000 meters)
      // Allow for some variance due to Earth's curvature calculations
      expect(distance, greaterThan(1100000)); // Greater than 1100 km
      expect(distance, lessThan(1200000)); // Less than 1200 km
    });

    test('should calculate zero distance for same coordinates', () {
      double lat = 19.0760;
      double lon = 72.8777;

      double distance = DistanceCalculator.calculateDistance(lat, lon, lat, lon);

      expect(distance, equals(0.0));
    });

    test('should format distance correctly in meters', () {
      double distance = 50.0; // 50 meters
      String formatted = DistanceCalculator.formatDistance(distance);
      expect(formatted, equals('50 m'));
    });

    test('should format distance correctly in kilometers', () {
      double distance = 1500.0; // 1500 meters = 1.5 km
      String formatted = DistanceCalculator.formatDistance(distance);
      expect(formatted, equals('1.50 km'));
    });

    test('should return true for distance within check-in range', () {
      double distance = 50.0; // 50 meters
      bool isWithinRange = DistanceCalculator.isWithinCheckInRange(distance);
      expect(isWithinRange, isTrue);
    });

    test('should return false for distance outside check-in range', () {
      double distance = 150.0; // 150 meters
      bool isWithinRange = DistanceCalculator.isWithinCheckInRange(distance);
      expect(isWithinRange, isFalse);
    });

    test('should return true for distance exactly at check-in range limit', () {
      double distance = 100.0; // Exactly 100 meters
      bool isWithinRange = DistanceCalculator.isWithinCheckInRange(distance);
      expect(isWithinRange, isTrue);
    });
  });
}
