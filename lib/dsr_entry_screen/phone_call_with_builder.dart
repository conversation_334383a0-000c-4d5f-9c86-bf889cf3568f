import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:http/http.dart' as http; // No longer needed

import '../theme/app_theme.dart';
import 'dsr_entry.dart';
import '../models.dart'; // Import your models
import '../api_service.dart'; // Import your ApiService

class PhoneCallWithBuilder extends StatefulWidget {
  const PhoneCallWithBuilder({super.key});

  @override
  State<PhoneCallWithBuilder> createState() => _PhoneCallWithBuilderState();
}

class _PhoneCallWithBuilderState extends State<PhoneCallWithBuilder> {
  final ApiService _apiService = ApiService();
  bool _isSubmitting = false;

  final _formKey = GlobalKey<FormState>();

  String? _processItem = 'Select';
  final List<String> _processdropdownItems = ['Select', 'Add', 'Update'];

  final TextEditingController _submissionDateController = TextEditingController();
  final TextEditingController _reportDateController = TextEditingController();
  DateTime? _selectedSubmissionDate;
  DateTime? _selectedReportDate;

  String? _areaCodeItem = 'Select'; // Changed from _areaCode to avoid conflict if AreaCodeDto is used later
  final List<String> _areaCodeItems = ['Select', 'Agra', 'Delhi', 'Mumbai'];

  String? _purchaserItem = 'Select';
  final List<String> _purchaserItems = ['Select', 'Purchaser(Non Trade)', 'AUTHORISED DEALER'];

  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _siteController = TextEditingController();
  final TextEditingController _contractorController = TextEditingController(); // For Bldrname
  String? _metWithItem = 'Select';
  final List<String> _metWithItems = ['Select', 'Builder', 'Contractor'];
  final TextEditingController _namedesgController = TextEditingController();
  final TextEditingController _topicController = TextEditingController();      // For Topcdiss
  final TextEditingController _ugaiRecoveryController = TextEditingController();
  final TextEditingController _grievanceController = TextEditingController();
  final TextEditingController _otherPointController = TextEditingController(); // For Remarksc

  List<File?> _selectedImages = [null]; // Max 3
  final List<int> _uploadRows = [0];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    // _apiService.currentLoginIdM = "your_actual_login_id";
    _selectedSubmissionDate = DateTime.now();
    _submissionDateController.text = DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
    _selectedReportDate = DateTime.now();
    _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
  }

  @override
  void dispose() {
    _submissionDateController.dispose();
    _reportDateController.dispose();
    _codeController.dispose();
    _siteController.dispose();
    _contractorController.dispose();
    _namedesgController.dispose();
    _topicController.dispose();
    _ugaiRecoveryController.dispose();
    _grievanceController.dispose();
    _otherPointController.dispose();
    super.dispose();
  }

  Future<void> _pickDate(bool isSubmissionDate) async {
    final now = DateTime.now();
    DateTime? initialDate = isSubmissionDate ? (_selectedSubmissionDate ?? now) : (_selectedReportDate ?? now);

    final picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      setState(() {
        if(isSubmissionDate) {
          _selectedSubmissionDate = picked;
          _submissionDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        } else {
          _selectedReportDate = picked;
          _reportDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        }
      });
    }
  }

  Future<void> _pickImage(int index) async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImages[index] = File(pickedFile.path);
      });
    }
  }

  void _addImageField() {
    if (_uploadRows.length >= 3) return;
    setState(() {
      _uploadRows.add(_uploadRows.length);
      _selectedImages.add(null);
    });
  }

  void _removeImageField(int index) {
    if (_uploadRows.length <= 1) return;
    setState(() {
      if (index < _uploadRows.length && index < _selectedImages.length) {
        _uploadRows.removeAt(index);
        _selectedImages.removeAt(index);
        for(int i=0; i < _uploadRows.length; i++) {
          _uploadRows[i] = i;
        }
      }
      if (_uploadRows.isEmpty) {
        _uploadRows.add(0);
        _selectedImages.add(null);
      }
    });
  }


  void _showImageDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
          child: InteractiveViewer(
            panEnabled: false,
            boundaryMargin: const EdgeInsets.all(20),
            minScale: 0.5,
            maxScale: 2,
            child: Image.file(imageFile, fit: BoxFit.contain),
          )
      ),
    );
  }

  Future<String> _fileToBase64(File? file) async {
    if (file == null) return "";
    final bytes = await file.readAsBytes();
    return base64Encode(bytes);
  }

  Future<void> _submitForm({bool exitAfter = false}) async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isSubmitting = true);

    final imgfirst = await _fileToBase64(_selectedImages.length > 0 ? _selectedImages[0] : null);
    final imgscndd = await _fileToBase64(_selectedImages.length > 1 ? _selectedImages[1] : null);
    final imgthird = await _fileToBase64(_selectedImages.length > 2 ? _selectedImages[2] : null);

    final dto = PhoneCallBuilderDto(
      proctype: _processItem == 'Select' ? 'Add' : _processItem!,
      submdate: _submissionDateController.text,
      repodate: _reportDateController.text,
      areacode: _areaCodeItem == 'Select' ? '' : _areaCodeItem!,
      purchser: _purchaserItem == 'Select' ? '' : _purchaserItem!,
      code: _codeController.text,
      sitename: _siteController.text,
      bldrname: _contractorController.text,
      metwith: _metWithItem == 'Select' ? '' : _metWithItem!,
      namedesg: _namedesgController.text,
      topcdiss: _topicController.text,
      ugairecov: _ugaiRecoveryController.text,
      grievance: _grievanceController.text,
      remarksc: _otherPointController.text,
      imgfirst: imgfirst,
      imgscndd: imgscndd,
      imgthird: imgthird,
    );

    try {
      // IMPORTANT: Replace "/specificactivity/phonecallbuilder" with your actual endpoint
      // OR refactor this DTO and logic to use the main DSR submission.
      await _apiService.submitPhoneCallBuilder(dto, "/dsractivity/phonecallbuilder"); // Placeholder

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Submitted!'), backgroundColor: Colors.green),
        );
        if (exitAfter) {
          Navigator.of(context).pop();
        } else {
          _resetForm();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Submission failed: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    setState(() {
      _processItem = 'Select';
      _selectedSubmissionDate = DateTime.now();
      _submissionDateController.text = DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
      _selectedReportDate = DateTime.now();
      _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
      _areaCodeItem = 'Select';
      _purchaserItem = 'Select';
      _codeController.clear();
      _siteController.clear();
      _contractorController.clear();
      _metWithItem = 'Select';
      _namedesgController.clear();
      _topicController.clear();
      _ugaiRecoveryController.clear();
      _grievanceController.clear();
      _otherPointController.clear();
      _uploadRows.clear();
      _selectedImages.clear();
      _uploadRows.add(0);
      _selectedImages.add(null);
    });
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () => Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const DsrEntry())),
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 22),
        ),
        title: Text('Phone Call With Builder', style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.white)),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              AppTheme.buildSectionCard(title: 'Process', icon: Icons.settings_outlined, children: [
                AppTheme.buildLabel('Process Type'), const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _processItem,
                  decoration: AppTheme.dropdownDecoration(hintText: 'Select Process'),
                  items: _processdropdownItems.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
                  onChanged: (val) => setState(() => _processItem = val),
                  validator: (value) => (value == null || value == 'Select') ? 'Required' : null,
                ),
              ]),
              AppTheme.buildSectionCard(title: 'Date Information', icon: Icons.date_range_outlined, children: [
                AppTheme.buildLabel('Submission Date'), const SizedBox(height: 8),
                AppTheme.buildDateField(context, _submissionDateController, () => _pickDate(true), 'Select Submission Date', initialDate: _selectedSubmissionDate),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Report Date'), const SizedBox(height: 8),
                AppTheme.buildDateField(context, _reportDateController, () => _pickDate(false), 'Select Report Date', initialDate: _selectedReportDate),
              ]),
              AppTheme.buildSectionCard(title: 'Location & Purchaser', icon: Icons.location_city_outlined, children: [
                AppTheme.buildLabel('Area Code'), const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _areaCodeItem,
                  decoration: AppTheme.dropdownDecoration(hintText: 'Select Area Code'),
                  items: _areaCodeItems.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
                  onChanged: (val) => setState(() => _areaCodeItem = val),
                  validator: (value) => (value == null || value == 'Select') ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Purchaser Type'), const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _purchaserItem,
                  decoration: AppTheme.dropdownDecoration(hintText: 'Select Purchaser Type'),
                  items: _purchaserItems.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
                  onChanged: (val) => setState(() => _purchaserItem = val),
                  validator: (value) => (value == null || value == 'Select') ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Purchaser Code'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Code', controller: _codeController),
              ]),
              AppTheme.buildSectionCard(title: 'Site & Contractor Details', icon: Icons.home_work_outlined, children: [
                AppTheme.buildLabel('Site Name'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Site Name', controller: _siteController),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Contractor Working at Site'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Contractor Name', controller: _contractorController),
              ]),
              AppTheme.buildSectionCard(title: 'Meeting & Discussion', icon: Icons.people_alt_outlined, children: [
                AppTheme.buildLabel('Met With'), const SizedBox(height: 8),
                DropdownButtonFormField<String>(
                  value: _metWithItem,
                  decoration: AppTheme.dropdownDecoration(hintText: 'Select Whom You Met'),
                  items: _metWithItems.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
                  onChanged: (val) => setState(() => _metWithItem = val),
                  validator: (value) => (value == null || value == 'Select') ? 'Required' : null,
                ),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Name and Designation of Person'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Name and Designation', controller: _namedesgController),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Topic Discussed'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Topic Discussed', controller: _topicController, maxLines: 3),
              ]),
              AppTheme.buildSectionCard(title: 'Follow-ups & Others', icon: Icons.assignment_turned_in_outlined, children: [
                AppTheme.buildLabel('Ugai Recovery Plans'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Ugai Recovery Plans', controller: _ugaiRecoveryController, maxLines: 3),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Any Purchaser Grievances'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Purchaser Grievances', controller: _grievanceController, maxLines: 3),
                const SizedBox(height: 16),
                AppTheme.buildLabel('Any other Point'), const SizedBox(height: 8),
                AppTheme.buildTextField('Enter Any Other Point', controller: _otherPointController, maxLines: 3),
              ]),

              Container( /* ... Image Upload UI from AnyOtherActivity ... */
                  margin: const EdgeInsets.only(top: 20, bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(children: [ const Icon(Icons.photo_library_rounded, color: AppTheme.primaryColor, size: 24), const SizedBox(width: 8), Text('Supporting Documents', style: Theme.of(context).textTheme.titleLarge?.copyWith(color: AppTheme.primaryColor))]),
                      const SizedBox(height: 4),
                      Text('Upload images if applicable', style: Theme.of(context).textTheme.bodyMedium),
                      const SizedBox(height: 16),
                      ...List.generate(_uploadRows.length, (index) {
                        return Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: _selectedImages[index] != null ? Colors.green.shade200 : Colors.grey.shade200, width: 1.5),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                    decoration: BoxDecoration(color: AppTheme.primaryColor.withOpacity(0.1), borderRadius: BorderRadius.circular(20)),
                                    child: Text('Document ${index + 1}', style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 14)),
                                  ),
                                  const Spacer(),
                                  if (_selectedImages[index] != null)
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                      decoration: BoxDecoration(color: Colors.green.shade100, borderRadius: BorderRadius.circular(20)),
                                      child: const Row(mainAxisSize: MainAxisSize.min, children: [ Icon(Icons.check_circle, color: Colors.green, size: 16), SizedBox(width: 4), Text('Uploaded', style: TextStyle(color: Colors.green, fontWeight: FontWeight.w500, fontSize: 14))]),
                                    ),
                                ]),
                                const SizedBox(height:16),
                                if (_selectedImages[index] != null)
                                  GestureDetector(
                                    onTap: () => _showImageDialog(_selectedImages[index]!),
                                    child: Container(
                                      height: 120, width: double.infinity, margin: const EdgeInsets.only(bottom:16),
                                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), image: DecorationImage(image: FileImage(_selectedImages[index]!), fit: BoxFit.cover)),
                                      child: Align(alignment: Alignment.topRight, child: Container(margin: const EdgeInsets.all(8), padding: const EdgeInsets.all(4), decoration: BoxDecoration(color: Colors.black.withOpacity(0.6), shape: BoxShape.circle), child: const Icon(Icons.zoom_in, color: Colors.white, size: 20))),
                                    ),
                                  ),
                                Row(children: [
                                  Expanded(child: ElevatedButton.icon(onPressed: () => _pickImage(index), icon: Icon(_selectedImages[index] != null ? Icons.refresh:Icons.upload_file, size:18), label: Text(_selectedImages[index] != null?'Replace':'Upload'), style: AppTheme.elevatedButtonStyle(bgColor: _selectedImages[index] != null ? Colors.amber.shade600 : AppTheme.primaryColor))),
                                  if (_selectedImages[index] != null) ...[
                                    const SizedBox(width: 8),
                                    Expanded(child: ElevatedButton.icon(onPressed:()=> _showImageDialog(_selectedImages[index]!), icon:const Icon(Icons.visibility, size:18), label:const Text('View'), style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor))),
                                  ]
                                ],)
                              ],
                            )
                        );
                      }),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (_uploadRows.length < 3) ElevatedButton.icon(onPressed: _addImageField, icon: const Icon(Icons.add_photo_alternate, size: 20), label: const Text('Add Document'), style: AppTheme.elevatedButtonStyle()),
                          if (_uploadRows.length > 1) ...[ const SizedBox(width: 12), ElevatedButton.icon(onPressed: () => _removeImageField(_uploadRows.length - 1), icon: const Icon(Icons.remove_circle_outline, size: 20), label: const Text('Remove Last'), style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.dangerButtonColor))]
                        ],
                      ),
                    ],
                  )
              ),

              const SizedBox(height: 24),
              Container( // Submit buttons card
                  padding: const EdgeInsets.all(20),
                  decoration: AppTheme.cardDecoration,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(children: [const Icon(Icons.save_alt_rounded, color: AppTheme.primaryColor, size:24), const SizedBox(width:8), Text('Submit Call Details', style:Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppTheme.primaryColor))]),
                      const SizedBox(height:20),
                      ElevatedButton(
                        onPressed: _isSubmitting ? null :() => _submitForm(exitAfter: false),
                        style: AppTheme.elevatedButtonStyle(),
                        child: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & New'),
                      ),
                      const SizedBox(height: 12),
                      ElevatedButton(
                        onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: true),
                        style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                        child: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & Exit'),
                      ),
                    ],
                  )
              ),
            ],
          ),
        ),
      ),
    );
  }
}