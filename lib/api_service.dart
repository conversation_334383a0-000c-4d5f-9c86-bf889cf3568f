// lib/api_service.dart
import 'dart:convert';
import 'package:dio/dio.dart';
import 'models.dart';

class ApiService {
  final Dio _dio = Dio();
  String currentLoginIdM = "default_login_id"; // Set this from auth
  
  // Base URL for API calls
  final String baseUrl = "https://api.example.com"; // Replace with your actual API base URL
  
  ApiService() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Add interceptors for logging, auth tokens, etc. if needed
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
    ));
  }
  
  // Get area codes with optional search filter
  Future<List<AreaCodeDto>> getAreaCodes({String? search}) async {
    try {
      final response = await _dio.get(
        '/api/areacodes',
        queryParameters: {
          if (search != null && search.isNotEmpty) 'search': search,
        },
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? [];
        return data.map((json) => AreaCodeDto.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load area codes: ${response.statusCode}');
      }
    } catch (e) {
      // For development/testing, return mock data
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        return _getMockAreaCodes(search);
      }
      throw Exception('Error fetching area codes: $e');
    }
  }
  
  // Get retailers with area code, customer/retailer flag, and optional search
  Future<List<RetailerInfoDto>> getRetailers({
    required String areaCode,
    required String cusRtlFl,
    String? search,
  }) async {
    try {
      final response = await _dio.get(
        '/api/retailers',
        queryParameters: {
          'areaCode': areaCode,
          'cusRtlFl': cusRtlFl,
          if (search != null && search.isNotEmpty) 'search': search,
        },
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'] ?? [];
        return data.map((json) => RetailerInfoDto.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load retailers: ${response.statusCode}');
      }
    } catch (e) {
      // For development/testing, return mock data
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        return _getMockRetailers(areaCode, cusRtlFl, search);
      }
      throw Exception('Error fetching retailers: $e');
    }
  }
  
  // Submit DSR header
  Future<Map<String, dynamic>> submitDsrHeader(DsrHeaderSubmitDto dto) async {
    try {
      final response = await _dio.post(
        '/api/dsr/header',
        data: jsonEncode(dto.toJson()),
      );
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to submit DSR header: ${response.statusCode}');
      }
    } catch (e) {
      // For development/testing, return mock response
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        return {
          'success': true,
          'message': 'DSR header submitted successfully (mock)',
          'generatedDocuNumb': 'DSR${DateTime.now().millisecondsSinceEpoch}',
        };
      }
      throw Exception('Error submitting DSR header: $e');
    }
  }
  
  // Submit Work From Home data
  Future<Map<String, dynamic>> submitWorkFromHome(WorkFromHomeDto dto, String endpoint) async {
    try {
      final response = await _dio.post(
        endpoint,
        data: jsonEncode(dto.toJson()),
      );
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data;
      } else {
        throw Exception('Failed to submit Work From Home data: ${response.statusCode}');
      }
    } catch (e) {
      // For development/testing, return mock response
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        return {
          'success': true,
          'message': 'Work From Home data submitted successfully (mock)',
        };
      }
      throw Exception('Error submitting Work From Home data: $e');
    }
  }
  
  // Mock data for development/testing
  List<AreaCodeDto> _getMockAreaCodes(String? search) {
    final mockData = [
      AreaCodeDto(areaCode: 'A001', areaName: 'North Region', areaType: 'Urban'),
      AreaCodeDto(areaCode: 'A002', areaName: 'South Region', areaType: 'Urban'),
      AreaCodeDto(areaCode: 'A003', areaName: 'East Region', areaType: 'Rural'),
      AreaCodeDto(areaCode: 'A004', areaName: 'West Region', areaType: 'Rural'),
      AreaCodeDto(areaCode: 'A005', areaName: 'Central Region', areaType: 'Mixed'),
    ];
    
    if (search == null || search.isEmpty) {
      return mockData;
    }
    
    final searchLower = search.toLowerCase();
    return mockData.where((area) => 
      area.areaCode.toLowerCase().contains(searchLower) || 
      (area.areaName?.toLowerCase().contains(searchLower) ?? false)
    ).toList();
  }
  
  List<RetailerInfoDto> _getMockRetailers(String areaCode, String cusRtlFl, String? search) {
    final mockData = [
      RetailerInfoDto(
        code: 'R001',
        name: 'ABC Retailers',
        address: '123 Main St, City',
        latitude: 28.6139,
        longitude: 77.2090,
        kycStatus: 'Verified',
        contactPerson: 'John Doe',
        contactNumber: '9876543210',
      ),
      RetailerInfoDto(
        code: 'R002',
        name: 'XYZ Stores',
        address: '456 Park Ave, Town',
        latitude: 28.6129,
        longitude: 77.2080,
        kycStatus: 'Pending',
        contactPerson: 'Jane Smith',
        contactNumber: '8765432109',
      ),
      RetailerInfoDto(
        code: 'R003',
        name: 'PQR Enterprises',
        address: '789 Market Rd, Village',
        latitude: 28.6119,
        longitude: 77.2070,
        kycStatus: 'Verified',
        contactPerson: 'Bob Johnson',
        contactNumber: '7654321098',
      ),
    ];
    
    // Filter by search term if provided
    if (search != null && search.isNotEmpty) {
      final searchLower = search.toLowerCase();
      return mockData.where((retailer) => 
        (retailer.code?.toLowerCase().contains(searchLower) ?? false) || 
        (retailer.name?.toLowerCase().contains(searchLower) ?? false)
      ).toList();
    }
    
    return mockData;
  }
}