// lib/models.dart
// This file contains data models used throughout the application

class AreaCodeDto {
  final String areaCode;
  final String? areaName;
  final String? areaType;

  AreaCodeDto({
    required this.areaCode,
    this.areaName,
    this.areaType,
  });

  factory AreaCodeDto.fromJson(Map<String, dynamic> json) {
    return AreaCodeDto(
      areaCode: json['areaCode'] ?? '',
      areaName: json['areaName'],
      areaType: json['areaType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'areaCode': areaCode,
      'areaName': areaName,
      'areaType': areaType,
    };
  }

  @override
  String toString() {
    return '$areaCode - ${areaName ?? 'Unknown Area'}';
  }
}

class RetailerInfoDto {
  final String? code;
  final String? name;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? kycStatus;
  final String? contactPerson;
  final String? contactNumber;

  RetailerInfoDto({
    this.code,
    this.name,
    this.address,
    this.latitude,
    this.longitude,
    this.kycStatus,
    this.contactPerson,
    this.contactNumber,
  });

  factory RetailerInfoDto.fromJson(Map<String, dynamic> json) {
    return RetailerInfoDto(
      code: json['code'],
      name: json['name'],
      address: json['address'],
      latitude: json['latitude'] != null ? double.tryParse(json['latitude'].toString()) : null,
      longitude: json['longitude'] != null ? double.tryParse(json['longitude'].toString()) : null,
      kycStatus: json['kycStatus'],
      contactPerson: json['contactPerson'],
      contactNumber: json['contactNumber'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'kycStatus': kycStatus,
      'contactPerson': contactPerson,
      'contactNumber': contactNumber,
    };
  }

  @override
  String toString() {
    return '$code - $name';
  }
}

class DsrHeaderSubmitDto {
  final String? loginIdM;
  final String? docuDate;
  final String? dsrParam;
  final String? cusRtlFl;
  final String? cusRtlCd;
  final String? areaCode;
  final String? cuRtType;
  final String? geoLatit;
  final String? geoLongt;
  final double? distance;
  final String? distanceExceptionReason;

  DsrHeaderSubmitDto({
    this.loginIdM,
    this.docuDate,
    this.dsrParam,
    this.cusRtlFl,
    this.cusRtlCd,
    this.areaCode,
    this.cuRtType,
    this.geoLatit,
    this.geoLongt,
    this.distance,
    this.distanceExceptionReason,
  });

  Map<String, dynamic> toJson() {
    return {
      'loginIdM': loginIdM,
      'docuDate': docuDate,
      'dsrParam': dsrParam,
      'cusRtlFl': cusRtlFl,
      'cusRtlCd': cusRtlCd,
      'areaCode': areaCode,
      'cuRtType': cuRtType,
      'geoLatit': geoLatit,
      'geoLongt': geoLongt,
      'distance': distance,
      'distanceExceptionReason': distanceExceptionReason,
    };
  }
}

class WorkFromHomeDto {
  final String proctype;
  final String submdate;
  final String repodate;
  final String imgfirst;
  final String imgscndd;
  final String imgthird;

  WorkFromHomeDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    required this.imgfirst,
    required this.imgscndd,
    required this.imgthird,
  });

  Map<String, dynamic> toJson() {
    return {
      'proctype': proctype,
      'submdate': submdate,
      'repodate': repodate,
      'imgfirst': imgfirst,
      'imgscndd': imgscndd,
      'imgthird': imgthird,
    };
  }
}