# User Account Blocking System - Backend Implementation Guide

## Overview
This guide provides the backend implementation requirements for the user account blocking system that works with the Flutter app modifications.

## Database Schema Changes

### 1. Add Blocking Column to Users Table

Add a boolean column to your existing users table:

```sql
-- For SQL Server
ALTER TABLE Users 
ADD is_blocked BIT NOT NULL DEFAULT 0;

-- For MySQL
ALTER TABLE users 
ADD COLUMN is_blocked BOOLEAN NOT NULL DEFAULT FALSE;

-- For PostgreSQL
ALTER TABLE users 
ADD COLUMN is_blocked BOOLEAN NOT NULL DEFAULT FALSE;
```

### 2. Optional: Add Blocking Metadata (Recommended)

For better tracking and management:

```sql
-- Additional columns for blocking management
ALTER TABLE Users 
ADD blocked_at DATETIME NULL,
ADD blocked_by <PERSON><PERSON>HA<PERSON>(255) NULL,
ADD block_reason TEXT NULL;
```

## API Endpoint Modifications

### Current Endpoint: `/api/Auth/login`

Modify your existing login endpoint to include blocking checks:

#### Request Format (Unchanged)
```json
{
  "userID": "string",
  "password": "string", 
  "appRegId": "string"
}
```

#### Response Formats

**1. Successful Login (Active User)**
```json
HTTP 200 OK
{
  "msg": "Authentication successful",
  "role": "Customer|Worker|Staff",
  "token": "jwt_token_here", // Optional
  "userId": "user_id",
  "blocked": false
}
```

**2. Blocked User Response**
```json
HTTP 403 Forbidden
{
  "blocked": true,
  "message": "Your account has been blocked. If you believe this is an error, please contact <NAME_EMAIL>"
}
```

**3. Invalid Credentials**
```json
HTTP 401 Unauthorized
{
  "msg": "Authentication failed",
  "blocked": false
}
```

## Implementation Logic

### Authentication Flow

```csharp
// Example C# implementation for ASP.NET Core
[HttpPost("login")]
public async Task<IActionResult> Login([FromBody] LoginRequest request)
{
    try
    {
        // Step 1: Validate input
        if (string.IsNullOrEmpty(request.UserID) || string.IsNullOrEmpty(request.Password))
        {
            return BadRequest(new { msg = "Invalid input" });
        }

        // Step 2: Find user by userID
        var user = await _userRepository.GetUserByIdAsync(request.UserID);
        if (user == null)
        {
            return Unauthorized(new { msg = "Authentication failed", blocked = false });
        }

        // Step 3: Verify password
        bool isPasswordValid = VerifyPassword(request.Password, user.PasswordHash);
        if (!isPasswordValid)
        {
            return Unauthorized(new { msg = "Authentication failed", blocked = false });
        }

        // Step 4: CRITICAL - Check if user is blocked AFTER credential verification
        if (user.IsBlocked)
        {
            return StatusCode(403, new 
            { 
                blocked = true, 
                message = "Your account has been blocked. If you believe this is an error, please contact <NAME_EMAIL>" 
            });
        }

        // Step 5: Generate token (if using JWT)
        var token = GenerateJwtToken(user);

        // Step 6: Update last login, device registration, etc.
        await UpdateUserLoginInfo(user.Id, request.AppRegId);

        // Step 7: Return success response
        return Ok(new
        {
            msg = "Authentication successful",
            role = user.Role,
            token = token,
            userId = user.Id,
            blocked = false
        });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Login error for user: {UserID}", request.UserID);
        return StatusCode(500, new { msg = "Internal server error" });
    }
}
```

### Key Implementation Points

1. **Credential Verification First**: Always verify username/password before checking blocked status
2. **Consistent Response Format**: Ensure blocked status is clearly indicated
3. **HTTP Status Codes**: Use 403 Forbidden for blocked users
4. **Security**: Don't reveal blocking status to invalid credential attempts
5. **Logging**: Log blocked login attempts for security monitoring

## Admin Management Endpoints (Optional)

### Block User
```http
POST /api/Admin/users/{userId}/block
Authorization: Bearer {admin_token}

{
  "reason": "Violation of terms of service",
  "blockedBy": "admin_user_id"
}
```

### Unblock User
```http
POST /api/Admin/users/{userId}/unblock
Authorization: Bearer {admin_token}

{
  "unblockedBy": "admin_user_id"
}
```

### Get Blocked Users
```http
GET /api/Admin/users/blocked
Authorization: Bearer {admin_token}
```

## Database Queries

### Check User Status
```sql
SELECT 
    user_id,
    username,
    password_hash,
    role,
    is_blocked,
    blocked_at,
    block_reason
FROM Users 
WHERE user_id = @userId;
```

### Block User
```sql
UPDATE Users 
SET 
    is_blocked = 1,
    blocked_at = GETDATE(),
    blocked_by = @adminUserId,
    block_reason = @reason
WHERE user_id = @userId;
```

### Unblock User
```sql
UPDATE Users 
SET 
    is_blocked = 0,
    blocked_at = NULL,
    blocked_by = NULL,
    block_reason = NULL
WHERE user_id = @userId;
```

## Security Considerations

1. **Rate Limiting**: Implement rate limiting on login attempts
2. **Audit Logging**: Log all blocking/unblocking actions
3. **Admin Authorization**: Ensure only authorized admins can block/unblock users
4. **Data Privacy**: Don't expose sensitive user information in error messages
5. **Consistent Timing**: Ensure blocked user responses don't reveal timing differences

## Testing Scenarios

### Test Cases to Implement

1. **Valid Active User**: Should login successfully
2. **Valid Blocked User**: Should return 403 with blocked message
3. **Invalid Credentials**: Should return 401 without revealing block status
4. **Non-existent User**: Should return 401 without revealing user existence
5. **Malformed Request**: Should return 400 Bad Request

### Sample Test Data

```sql
-- Insert test users
INSERT INTO Users (user_id, username, password_hash, role, is_blocked) VALUES
('test_active', 'activeuser', 'hashed_password', 'Customer', 0),
('test_blocked', 'blockeduser', 'hashed_password', 'Customer', 1);
```

## Deployment Checklist

- [ ] Database schema updated with is_blocked column
- [ ] Login endpoint modified to check blocked status
- [ ] Response format updated to include blocked field
- [ ] Admin endpoints implemented (if required)
- [ ] Audit logging implemented
- [ ] Rate limiting configured
- [ ] Test cases written and passing
- [ ] Documentation updated
- [ ] Security review completed

## Support Configuration

Update the support email in your Flutter app's BlockedUserDialog:

```dart
// In lib/widgets/blocked_user_dialog.dart
supportEmail: '<EMAIL>'

// In lib/screens/login_screen.dart
supportEmail: '<EMAIL>'
```

This implementation ensures that blocked users cannot access your application while providing a clear path for support contact.
