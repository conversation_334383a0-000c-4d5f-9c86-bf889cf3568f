// lib/services/api_client.dart

import 'package:learning2/services/auth_service.dart';

/// Calls your backend /api/Auth/login for username/password authentication.
class ApiClient {
  // Replace with your actual server base URL:
  static const String apiBaseUrl = 'https://192.168.55.182:7023';

  /// Attempts to log in with [userID], [password], and [appRegId].
  /// Returns an AuthResponse indicating success, blocked, etc.
  static Future<AuthResponse> loginWithCredentials({
    required String userID,
    required String password,
    required String appRegId,
  }) async {
    // Use AuthService directly instead of duplicating logic
    return await AuthService.authenticateUser(
      userID: userID,
      password: password,
      appRegId: appRegId,
    );
  }
}
