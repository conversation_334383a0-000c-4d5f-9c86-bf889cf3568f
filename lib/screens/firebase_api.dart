import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
// Temporarily commenting out flutter_local_notifications due to compatibility issues
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:learning2/screens/notification_screen.dart';

class FirebaseApi {
  final _firebaseMessaging = FirebaseMessaging.instance;
  // Temporarily commenting out flutter_local_notifications due to compatibility issues
  // final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
  //     FlutterLocalNotificationsPlugin();

  // Add a field to store the notification provider
  NotificationProvider? _notificationProvider;

  Future<void> handleBackgroundMessage(RemoteMessage message) async {
    // Log notification details for debugging
    debugPrint("Firebase Background Message - Title: ${message.notification?.title}");
    debugPrint("Firebase Background Message - Body: ${message.notification?.body}");
    debugPrint("Firebase Background Message - Data: ${message.data}");
  }

  void handleForegroundMessage(RemoteMessage message) {
    debugPrint("Firebase Foreground Message:");
    debugPrint("Title: ${message.notification?.title}");
    debugPrint("Body: ${message.notification?.body}");
    debugPrint("Data: ${message.data}");

    // Show local notification
    _showLocalNotification(message);

    // Add to notification provider if available
    if (_notificationProvider != null) {
      _notificationProvider!.addFirebaseNotification(
        message.notification?.title,
        message.notification?.body,
      );
    }
  }

  Future<void> _showLocalNotification(RemoteMessage message) async {
    // Temporarily commenting out flutter_local_notifications due to compatibility issues
    // final notification = message.notification;
    // final android = message.notification?.android;
    //
    // if (notification != null && android != null) {
    //   await _flutterLocalNotificationsPlugin.show(
    //     notification.hashCode,
    //     notification.title,
    //     notification.body,
    //     const NotificationDetails(
    //       android: AndroidNotificationDetails(
    //         'high_importance_channel',
    //         'High Importance Notifications',
    //         importance: Importance.max,
    //         priority: Priority.high,
    //       ),
    //     ),
    //     payload: message.data.toString(),
    //   );
    // }

    // Log the notification for debugging
    debugPrint('Local notification would show: ${message.notification?.title}');
  }

  Future<void> _initLocalNotifications() async {
    // Temporarily commenting out flutter_local_notifications due to compatibility issues
    // const AndroidInitializationSettings initializationSettingsAndroid =
    //     AndroidInitializationSettings('@mipmap/ic_launcher');
    //
    // const InitializationSettings initializationSettings =
    //     InitializationSettings(android: initializationSettingsAndroid);
    //
    // await _flutterLocalNotificationsPlugin.initialize(initializationSettings);

    // Log initialization for debugging
    debugPrint('Local notifications would be initialized');
  }

  Future<void> initNotification([
    NotificationProvider? notificationProvider,
  ]) async {
    // Store the notification provider if provided
    _notificationProvider = notificationProvider;

    await _firebaseMessaging.requestPermission();
    final fcmToken = await _firebaseMessaging.getToken();
    debugPrint('Firebase FCM Token: $fcmToken');

    await _initLocalNotifications();

    // Temporarily commenting out background message handler due to compatibility issues
    // FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);
    FirebaseMessaging.onMessage.listen(handleForegroundMessage);
  }
}
