# User Account Blocking System - Testing Guide

## Overview
This guide provides comprehensive testing instructions for the user account blocking system implementation.

## Prerequisites
1. Backend API must be updated according to `BACKEND_IMPLEMENTATION_GUIDE.md`
2. Flutter app dependencies installed (`flutter pub get` completed)
3. Test user accounts created in your database

## Test User Setup

Create these test accounts in your database:

```sql
-- Active user for successful login testing
INSERT INTO Users (user_id, username, password_hash, role, is_blocked) VALUES
('test_active', 'activeuser', 'your_hashed_password', 'Customer', 0);

-- Blocked user for blocking system testing
INSERT INTO Users (user_id, username, password_hash, role, is_blocked, blocked_at, block_reason) VALUES
('test_blocked', 'blockeduser', 'your_hashed_password', 'Customer', 1, GETDATE(), 'Account suspended for testing');

-- Worker role user for role-based navigation testing
INSERT INTO Users (user_id, username, password_hash, role, is_blocked) VALUES
('test_worker', 'workeruser', 'your_hashed_password', 'Worker', 0);
```

## Flutter App Testing Scenarios

### Test Case 1: Active User Login (Success Flow)

**Steps:**
1. Launch the Flutter app
2. Enter credentials for active user:
   - Username: `activeuser`
   - Password: `[correct_password]`
3. Tap "LOGIN" button

**Expected Results:**
- Loading indicator appears
- API call succeeds with HTTP 200
- User is navigated to appropriate home screen based on role
- No authentication tokens are saved to SharedPreferences
- `isLoggedIn` is set to `true` in SharedPreferences

**Verification:**
```dart
// Check SharedPreferences
final prefs = await SharedPreferences.getInstance();
assert(prefs.getBool('isLoggedIn') == true);
assert(prefs.getString('userRole') == 'Customer'); // or 'Worker'
```

### Test Case 2: Blocked User Login (Blocking Flow)

**Steps:**
1. Launch the Flutter app
2. Enter credentials for blocked user:
   - Username: `blockeduser`
   - Password: `[correct_password]`
3. Tap "LOGIN" button

**Expected Results:**
- Loading indicator appears
- API call returns HTTP 403 or blocked:true
- **CRITICAL**: No navigation occurs
- **CRITICAL**: No authentication data is saved
- Blocked user dialog appears with:
  - Title: "Access Denied"
  - Red block icon
  - Custom message from backend
  - "Contact Support" button
  - "OK" button

**Verification:**
```dart
// Verify NO authentication data is saved
final prefs = await SharedPreferences.getInstance();
assert(prefs.getBool('isLoggedIn') != true);
assert(prefs.getString('userRole') == null);
assert(prefs.getString('authToken') == null);
```

### Test Case 3: Invalid Credentials

**Steps:**
1. Enter invalid credentials:
   - Username: `activeuser`
   - Password: `wrong_password`
2. Tap "LOGIN" button

**Expected Results:**
- Loading indicator appears
- API call returns HTTP 401
- Standard error dialog appears (not blocked dialog)
- No navigation occurs
- No authentication data is saved

### Test Case 4: Contact Support Functionality

**Steps:**
1. Trigger blocked user dialog (Test Case 2)
2. Tap "Contact Support" button

**Expected Results:**
- Email client opens with pre-filled email:
  - To: `<EMAIL>`
  - Subject: "Account Access Issue - Blocked Account"
  - Body: Pre-written support request
- If email client unavailable, snackbar appears with support email

### Test Case 5: Network Error Handling

**Steps:**
1. Disable internet connection
2. Attempt login with any credentials
3. Tap "LOGIN" button

**Expected Results:**
- Network error dialog appears
- No navigation occurs
- No authentication data is saved

### Test Case 6: Role-Based Navigation

**Steps:**
1. Login with Worker role user:
   - Username: `workeruser`
   - Password: `[correct_password]`

**Expected Results:**
- User navigates to `WorkerHomeScreen`
- Correct role saved in SharedPreferences

## Backend API Testing

### Test API Endpoints Directly

Use tools like Postman or curl to test your backend:

#### Test 1: Active User Login
```bash
curl -X POST https://your-api-url/api/Auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "userID": "activeuser",
    "password": "correct_password",
    "appRegId": "test_device"
  }'
```

**Expected Response:**
```json
HTTP 200 OK
{
  "msg": "Authentication successful",
  "role": "Customer",
  "blocked": false,
  "token": "jwt_token_here"
}
```

#### Test 2: Blocked User Login
```bash
curl -X POST https://your-api-url/api/Auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "userID": "blockeduser", 
    "password": "correct_password",
    "appRegId": "test_device"
  }'
```

**Expected Response:**
```json
HTTP 403 Forbidden
{
  "blocked": true,
  "message": "Your account has been blocked. If you believe this is an error, please contact <NAME_EMAIL>"
}
```

## Security Testing

### Test 1: Timing Attack Prevention
- Verify that blocked user responses don't have significantly different response times
- Both valid blocked and invalid credential responses should take similar time

### Test 2: Information Disclosure
- Ensure invalid credentials don't reveal if user is blocked
- Blocked status should only be revealed after successful credential verification

### Test 3: Rate Limiting
- Test multiple rapid login attempts
- Verify rate limiting is working correctly

## Automated Testing

### Unit Tests for AuthService

Create unit tests for the AuthService:

```dart
// test/services/auth_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:learning2/services/auth_service.dart';

void main() {
  group('AuthService Tests', () {
    test('should return blocked result for blocked user', () async {
      // Mock HTTP response for blocked user
      // Test AuthService.authenticateUser method
      // Assert result is AuthResult.blocked
    });

    test('should return success result for active user', () async {
      // Mock HTTP response for active user
      // Test AuthService.authenticateUser method
      // Assert result is AuthResult.success
    });

    test('should save auth data correctly', () async {
      // Test AuthService.saveAuthData method
      // Verify SharedPreferences values
    });
  });
}
```

### Widget Tests for BlockedUserDialog

```dart
// test/widgets/blocked_user_dialog_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:learning2/widgets/blocked_user_dialog.dart';

void main() {
  testWidgets('BlockedUserDialog displays correctly', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: BlockedUserDialog(
            message: 'Test blocked message',
            supportEmail: '<EMAIL>',
          ),
        ),
      ),
    );

    expect(find.text('Access Denied'), findsOneWidget);
    expect(find.text('Test blocked message'), findsOneWidget);
    expect(find.text('Contact Support'), findsOneWidget);
    expect(find.text('OK'), findsOneWidget);
  });
}
```

## Performance Testing

### Test 1: Login Response Time
- Measure login response times for both active and blocked users
- Ensure acceptable performance (< 3 seconds typical)

### Test 2: Memory Usage
- Monitor memory usage during login flows
- Verify no memory leaks in authentication process

## Regression Testing Checklist

After implementing the blocking system, verify these existing features still work:

- [ ] Normal login flow for active users
- [ ] OTP login functionality
- [ ] Role-based navigation (Customer/Worker/Staff)
- [ ] Remember me functionality (if implemented)
- [ ] Logout functionality
- [ ] Session persistence across app restarts
- [ ] Firebase messaging integration
- [ ] Background services and location tracking

## Production Testing

### Pre-deployment Checklist

- [ ] All unit tests passing
- [ ] All widget tests passing
- [ ] Integration tests completed
- [ ] Backend API tests passing
- [ ] Security testing completed
- [ ] Performance testing acceptable
- [ ] User acceptance testing completed
- [ ] Support email configured correctly
- [ ] Error logging and monitoring configured

### Post-deployment Monitoring

Monitor these metrics after deployment:

1. **Login Success Rate**: Should remain high for active users
2. **Blocked Login Attempts**: Track frequency of blocked user login attempts
3. **Support Requests**: Monitor increase in support emails
4. **Error Rates**: Watch for any increase in authentication errors
5. **Performance**: Monitor login response times

## Troubleshooting Common Issues

### Issue 1: Blocked Dialog Not Appearing
- Check backend API response format
- Verify HTTP status codes (should be 403 for blocked users)
- Check Flutter console for authentication errors

### Issue 2: Email Client Not Opening
- Verify `url_launcher` dependency is installed
- Check device has email client configured
- Test fallback snackbar functionality

### Issue 3: Authentication Data Still Saved for Blocked Users
- Verify the switch statement in login_screen.dart
- Ensure blocked case doesn't call saveAuthData
- Check SharedPreferences after blocked login attempt

### Issue 4: Navigation Still Occurs for Blocked Users
- Verify the switch statement handles blocked case correctly
- Ensure no navigation code in blocked case
- Check for any async timing issues

This comprehensive testing approach ensures the blocking system works correctly and securely while maintaining existing functionality.
