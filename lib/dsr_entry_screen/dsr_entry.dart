// lib/dsr_entry.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models.dart';
import '../api_service.dart';
import '../theme/app_theme.dart';
import 'BtlActivity.dart';
import 'CheckSampling.dart';
import 'DsrVisit.dart';
import 'MeetingWithNewPurchaser.dart';
import 'MeetingsWithContractor.dart';
import 'InternalTeamMeeting.dart';
import 'OfficeWork.dart';
import 'OnLeave.dart';
import 'PhoneCallWithBuilder.dart';
import 'PhoneCallWithUnregisterdPurchaser.dart';
import 'WorkFromHome.dart';
import 'AnyOtherActivity.dart'; // if you have a catch‐all “Any Other Activity” screen

class DsrEntry extends StatefulWidget {
  const DsrEntry({Key? key}) : super(key: key);

  @override
  State<DsrEntry> createState() => _DsrEntryState();
}

class _DsrEntryState extends State<DsrEntry> {
  final ApiService _apiService = ApiService();

  // Header fields
  final TextEditingController _loginIdController = TextEditingController();
  final TextEditingController _docNumberController = TextEditingController();
  DateTime? _selectedDate;
  final TextEditingController _dateController = TextEditingController();

  // Area code and Retailer selection
  AreaCodeDto? _selectedAreaCode;
  RetailerInfoDto? _selectedRetailer;

  // For “Customer / Retailer” flag – either “C” or “R”
  String? _cusRtlFlag = 'Retailer';
  final List<String> _cusRtlOptions = ['Retailer', 'Customer'];

  // State variables
  bool _isLoadingRetailers = false;
  List<RetailerInfoDto> _retailers = [];

  final _headerFormKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    // Pre‐fill date with today
    _selectedDate = DateTime.now();
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);

    // (OPTIONAL) If you have an authenticated login, you might set _loginIdController.text here
    // _loginIdController.text = “(current user login ID)”
  }

  @override
  void dispose() {
    _loginIdController.dispose();
    _docNumberController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _pickHeaderDate() async {
    final now = DateTime.now();
    DateTime initial = _selectedDate ?? now;
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initial,
      firstDate: DateTime(2000),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  /// Called when the user selects an area code. We then fetch retailers for that area.
  Future<void> _onAreaCodeChanged(AreaCodeDto? area) async {
    setState(() {
      _selectedAreaCode = area;
      _selectedRetailer = null;
      _retailers = [];
      _isLoadingRetailers = true;
    });

    if (area != null) {
      try {
        final List<RetailerInfoDto> fetched = await _apiService.getRetailers(
          areaCode: area.areaCode,
          cusRtlFl: _cusRtlFlag == 'Retailer' ? 'R' : 'C',
          search: null,
        );
        setState(() {
          _retailers = fetched;
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load retailers: $e'),
            backgroundColor: Colors.redAccent,
          ),
        );
      }
    }

    setState(() {
      _isLoadingRetailers = false;
    });
  }

  /// Submits the DSR header, then navigates into the selected activity.
  Future<void> _submitHeaderAndNavigate(Widget Function() nextPageBuilder) async {
    if (!_headerFormKey.currentState!.validate()) {
      return;
    }

    final dto = DsrHeaderSubmitDto(
      loginIdM: _loginIdController.text.trim(),
      docuNumb: _docNumberController.text.trim(),
      docuDate: _dateController.text.trim(),
      dsrParam: '05', // if this is a fixed parameter
      cusRtlFl: _cusRtlFlag == 'Retailer' ? 'R' : 'C',
      areaCode: _selectedAreaCode?.areaCode,
      cusRtlCd: _selectedRetailer?.code,
      cusRtlNm: _selectedRetailer?.name,
    );

    setState(() {
      _isLoadingRetailers = true;
    });

    try {
      final response = await _apiService.submitDsrHeader(dto);
      if (response['success'] == true) {
        final generatedDoc = response['generatedDocuNumb'] as String? ?? dto.docuNumb!;
        // Pass the generated document number (or the one user typed) forward
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => nextPageBuilder().runtimeType == DsrVisit
                ? DsrVisit(
              docuNumb: generatedDoc,
              areaCode: _selectedAreaCode!.areaCode,
              retailerCode: _selectedRetailer!.code!,
              retailerName: _selectedRetailer!.name!,
              loginId: _loginIdController.text.trim(),
            )
                : nextPageBuilder(),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['message'] ?? 'Submit failed'),
            backgroundColor: Colors.redAccent,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error submitting header: $e'),
          backgroundColor: Colors.redAccent,
        ),
      );
    } finally {
      setState(() {
        _isLoadingRetailers = false;
      });
    }
  }

  /// UI for choosing one of the nine DSR activities. Adjust labels/icons as needed.
  Widget _buildActivityGrid() {
    final List<_ActivityTile> tiles = [
      _ActivityTile(
        label: 'DSR Visit',
        icon: Icons.store_mall_directory_outlined,
        builder: () => DsrVisit(
          docuNumb: _docNumberController.text.trim(),
          areaCode: _selectedAreaCode!.areaCode,
          retailerCode: _selectedRetailer!.code!,
          retailerName: _selectedRetailer!.name!,
          loginId: _loginIdController.text.trim(),
        ),
      ),
      _ActivityTile(
        label: 'Work From Home',
        icon: Icons.home_outlined,
        builder: () => WorkFromHome(),
      ),
      _ActivityTile(
        label: 'Office Work',
        icon: Icons.work_outline,
        builder: () => OfficeWork(),
      ),
      _ActivityTile(
        label: 'On Leave',
        icon: Icons.beach_access_outlined,
        builder: () => OnLeave(),
      ),
      _ActivityTile(
        label: 'BTL Activity',
        icon: Icons.local_offer_outlined,
        builder: () => BtlActivity(),
      ),
      _ActivityTile(
        label: 'Check Sampling',
        icon: Icons.check_circle_outline,
        builder: () => CheckSampling(),
      ),
      _ActivityTile(
        label: 'Meeting - New Purchaser',
        icon: Icons.person_add_alt_1_outlined,
        builder: () => MeetingWithNewPurchaser(),
      ),
      _ActivityTile(
        label: 'Meeting - Contractor',
        icon: Icons.construction_outlined,
        builder: () => MeetingsWithContractor(),
      ),
      _ActivityTile(
        label: 'Internal Team Mtg',
        icon: Icons.group_outlined,
        builder: () => InternalTeamMeeting(),
      ),
      _ActivityTile(
        label: 'Phone Call - Builder',
        icon: Icons.phone_callback_outlined,
        builder: () => PhoneCallWithBuilder(),
      ),
      _ActivityTile(
        label: 'Phone Call - Unreg. Purchaser',
        icon: Icons.phone_missed_outlined,
        builder: () => const PhoneCallWithUnregisterdPurchaser(),
      ),
      _ActivityTile(
        label: 'Any Other Activity',
        icon: Icons.more_horiz_outlined,
        builder: () => AnyOtherActivity(),
      ),
    ];

    return GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: tiles.length,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 0.9,
      ),
      itemBuilder: (context, idx) {
        final tile = tiles[idx];
        return GestureDetector(
          onTap: () async {
            // First ensure header is submitted
            if (_selectedAreaCode == null || _selectedRetailer == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select Area Code & Retailer first.'),
                  backgroundColor: Colors.orange,
                ),
              );
              return;
            }
            if (_loginIdController.text.trim().isEmpty ||
                _docNumberController.text.trim().isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter Login ID and Document Number.'),
                  backgroundColor: Colors.orange,
                ),
              );
              return;
            }
            await _submitHeaderAndNavigate(tile.builder);
          },
          child: Card(
            elevation: 2,
            shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(tile.icon, size: 36, color: AppTheme.primaryColor),
                const SizedBox(height: 8),
                Text(
                  tile.label,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Async dropdown for Area Codes
  Widget _buildAreaCodeField() {
    return DropdownSearch<AreaCodeDto>(
      asyncItems: (filter) async {
        try {
          return await _apiService.getAreaCodes(search: filter);
        } catch (_) {
          return [];
        }
      },
      selectedItem: _selectedAreaCode,
      dropdownBuilder: (context, selected) {
        return Text(selected?.toString() ?? 'Select Area Code');
      },
      popupProps: PopupProps.menu(
        showSearchBox: true,
        searchFieldProps: TextFieldProps(
          decoration: AppTheme.textFieldInputDecoration(hintText: 'Search area codes...'),
        ),
        itemBuilder: (context, item, isSelected) {
          return ListTile(
            title: Text(item.toString()),
          );
        },
        emptyBuilder: (context, searchEntry) => const Center(
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Text('No area codes found'),
          ),
        ),
      ),
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration:
        AppTheme.dropdownDecoration(hintText: 'Area Code *'),
      ),
      compareFn: (a, b) => a?.areaCode == b?.areaCode,
      onChanged: _onAreaCodeChanged,
      validator: (value) =>
      value == null ? 'Please select an Area Code' : null,
      itemAsString: (dto) => dto?.toString() ?? '',
    );
  }

  /// Simple dropdown for Retailer (populated once area is chosen)
  Widget _buildRetailerField() {
    return _isLoadingRetailers
        ? const Center(child: Padding(
      padding: EdgeInsets.symmetric(vertical: 12),
      child: CircularProgressIndicator(),
    ))
        : DropdownButtonFormField<RetailerInfoDto>(
      value: _selectedRetailer,
      decoration: AppTheme.dropdownDecoration(hintText: 'Select Retailer *'),
      isExpanded: true,
      items: _retailers
          .map(
            (r) => DropdownMenuItem<RetailerInfoDto>(
          value: r,
          child: Text('${r.code} - ${r.name}'),
        ),
      )
          .toList(),
      onChanged: (r) => setState(() => _selectedRetailer = r),
      validator: (val) => val == null ? 'Please select a retailer' : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('DSR Entry'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _headerFormKey,
          child: ListView(
            children: [
              AppTheme.buildSectionCard(
                title: 'Header Details',
                icon: Icons.note_alt_outlined,
                children: [
                  AppTheme.buildLabel('Login ID *'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField(
                    'Enter Login ID',
                    controller: _loginIdController,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Document Number *'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField(
                    'Enter Document Number',
                    controller: _docNumberController,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Date *'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(
                    context,
                    _dateController,
                    _pickHeaderDate,
                    'Select Date',
                    initialDate: _selectedDate,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Purchaser Type *'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _cusRtlFlag,
                    decoration:
                    AppTheme.dropdownDecoration(hintText: 'Select Type'),
                    items: _cusRtlOptions
                        .map((opt) =>
                        DropdownMenuItem(value: opt, child: Text(opt)))
                        .toList(),
                    onChanged: (val) {
                      if (val != null) {
                        setState(() {
                          _cusRtlFlag = val;
                          // If an area code is already chosen, re-fetch retailers:
                          _onAreaCodeChanged(_selectedAreaCode);
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Area Code *'),
                  const SizedBox(height: 8),
                  _buildAreaCodeField(),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Retailer / Customer *'),
                  const SizedBox(height: 8),
                  _buildRetailerField(),
                ],
              ),

              const SizedBox(height: 24),
              AppTheme.buildLabel('Select an Activity'),
              const SizedBox(height: 12),
              _buildActivityGrid(),
            ],
          ),
        ),
      ),
    );
  }
}

/// Helper to represent each activity tile
class _ActivityTile {
  final String label;
  final IconData icon;
  final Widget Function() builder;

  _ActivityTile({
    required this.label,
    required this.icon,
    required this.builder,
  });
}
