// lib/api_service.dart

import 'dart:convert';
import 'package:dio/dio.dart';
import 'models.dart';

class ApiService {
  final Dio _dio;
  String currentLoginIdM = "default_login_id"; // Set this from auth

  /// You can inject baseUrl (e.g. via flavors or environment) if needed.
  ApiService({String baseUrl = "https://api.example.com"})
      : _dio = Dio(BaseOptions(
    baseUrl: baseUrl,
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  )) {
    // Logging interceptor
    _dio.interceptors.add(
      LogInterceptor(requestBody: true, responseBody: true),
    );

    // Example: if you need to attach an auth‐token to every request, do it here:
    // _dio.interceptors.add(InterceptorsWrapper(
    //   onRequest: (options, handler) async {
    //     final token = await _getTokenFromStorage();
    //     if (token != null) {
    //       options.headers['Authorization'] = 'Bearer $token';
    //     }
    //     return handler.next(options);
    //   },
    // ));
  }

  // ================================
  // 1) GENERIC GET‐LIST HELPER
  // ================================
  Future<List<T>> _getList<T>(
      String path,
      List<T> Function(List<dynamic>) fromJsonList, {
        Map<String, dynamic>? queryParameters,
      }) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);

      if (response.statusCode == 200) {
        final rawData = response.data;
        if (rawData is Map<String, dynamic> && rawData.containsKey('data')) {
          final List<dynamic> dataList = rawData['data'] as List<dynamic>;
          return fromJsonList(dataList);
        }
        throw Exception('Unexpected response format at $path');
      }

      throw Exception('GET $path failed: HTTP ${response.statusCode}');
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        // Fallback to mock based on the requested path:
        if (path.contains('/api/areacodes')) {
          // Extract optional "search" from queryParameters
          final search = queryParameters?['search'] as String?;
          return _getMockAreaCodes(search) as List<T>;
        }
        if (path.contains('/api/retailers')) {
          final areaCode = queryParameters?['areaCode'] as String;
          final cusRtlFl = queryParameters?['cusRtlFl'] as String;
          final search = queryParameters?['search'] as String?;
          return _getMockRetailers(areaCode, cusRtlFl, search) as List<T>;
        }
      }
      rethrow;
    }
  }

  /// Fetch list of AreaCodeDto, optionally filtered by [search].
  Future<List<AreaCodeDto>> getAreaCodes({String? search}) {
    return _getList<AreaCodeDto>(
      '/api/areacodes',
          (list) => list.map((j) => AreaCodeDto.fromJson(j)).toList(),
      queryParameters: {
        if (search != null && search.isNotEmpty) 'search': search,
      },
    );
  }

  /// Fetch list of RetailerInfoDto, filtered by [areaCode], [cusRtlFl], and optional [search].
  Future<List<RetailerInfoDto>> getRetailers({
    required String areaCode,
    required String cusRtlFl,
    String? search,
  }) {
    return _getList<RetailerInfoDto>(
      '/api/retailers',
          (list) => list.map((j) => RetailerInfoDto.fromJson(j)).toList(),
      queryParameters: {
        'areaCode': areaCode,
        'cusRtlFl': cusRtlFl,
        if (search != null && search.isNotEmpty) 'search': search,
      },
    );
  }

  // ================================
  // 2) GENERIC POST‐WITH‐MOCK HELPER
  // ================================
  Future<Map<String, dynamic>> _postWithMock<T>(
      T dto,
      String path, {
        required String successMockMessage,
      }) async {
    try {
      // Encode the .toJson() of the DTO
      final bodyJson = (dto as dynamic).toJson();
      final response = await _dio.post(path, data: jsonEncode(bodyJson));

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = response.data;
        if (data is Map<String, dynamic>) {
          return data;
        }
        // If the API returns a raw non‐Map (rare), wrap it
        return {'success': true, 'data': data};
      }

      throw Exception('POST $path failed: HTTP ${response.statusCode}');
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        return {
          'success': true,
          'message': successMockMessage,
        };
      }
      rethrow;
    }
  }

  /// Submit DSR header and return the API’s JSON response as a Map.
  Future<Map<String, dynamic>> submitDsrHeader(DsrHeaderSubmitDto dto) {
    return _postWithMock<DsrHeaderSubmitDto>(
      dto,
      '/api/dsr/header',
      successMockMessage: 'DSR header submitted successfully (mock)',
    );
  }

  /// Submit Work From Home DTO. Endpoint is hard‐coded internally.
  Future<Map<String, dynamic>> submitWorkFromHome(WorkFromHomeDto dto) {
    return _postWithMock<WorkFromHomeDto>(
      dto,
      '/dsractivity/workfromhome',
      successMockMessage: 'Work From Home data submitted successfully (mock)',
    );
  }

  /// Submit “Any Other Activity” DTO.
  Future<Map<String, dynamic>> submitAnyOtherActivity(AnyOtherActivityDto dto, String s) {
    return _postWithMock<AnyOtherActivityDto>(
      dto,
      '/dsractivity/anyotheractivity',
      successMockMessage: 'Any Other Activity data submitted successfully (mock)',
    );
  }

  /// Submit Full DSR DTO.
  Future<Map<String, dynamic>> submitFullDsr(DsrActivityFullDto dto) {
    return _postWithMock<DsrActivityFullDto>(
      dto,
      '/api/dsr/full',
      successMockMessage: 'Full DSR data submitted successfully (mock)',
    );
  }

  /// Submit BTL Activity DTO.
  Future<Map<String, dynamic>> submitBtlActivity(BtlActivityDto dto, String s) {
    return _postWithMock<BtlActivityDto>(
      dto,
      '/dsractivity/btlactivity',
      successMockMessage: 'BTL Activity data submitted successfully (mock)',
    );
  }

  /// Submit Check Sampling DTO.
  Future<Map<String, dynamic>> submitCheckSampling(CheckSamplingDto dto, String s) {
    return _postWithMock<CheckSamplingDto>(
      dto,
      '/dsractivity/checksampling',
      successMockMessage: 'Check Sampling data submitted successfully (mock)',
    );
  }

  /// Submit Meeting With New Purchaser DTO.
  Future<Map<String, dynamic>> submitMeetingNewPurchaser(MeetingNewPurchaserDto dto) {
    return _postWithMock<MeetingNewPurchaserDto>(
      dto,
      '/dsractivity/meetingnewpurchaser',
      successMockMessage: 'Meeting New Purchaser data submitted successfully (mock)',
    );
  }

  /// Submit Meeting With Contractor DTO.
  Future<Map<String, dynamic>> submitMeetingContractor(MeetingContractorDto dto) {
    return _postWithMock<MeetingContractorDto>(
      dto,
      '/dsractivity/meetingcontractor',
      successMockMessage: 'Meeting Contractor data submitted successfully (mock)',
    );
  }

  /// Submit Internal Team Meeting DTO.
  Future<Map<String, dynamic>> submitInternalTeamMeeting(InternalTeamMeetingDto dto) {
    return _postWithMock<InternalTeamMeetingDto>(
      dto,
      '/dsractivity/internalteammeeting',
      successMockMessage: 'Internal Team Meeting data submitted successfully (mock)',
    );
  }

  /// Submit Office Work DTO.
  Future<Map<String, dynamic>> submitOfficeWork(OfficeWorkDto dto) {
    return _postWithMock<OfficeWorkDto>(
      dto,
      '/dsractivity/officework',
      successMockMessage: 'Office Work data submitted successfully (mock)',
    );
  }

  /// Submit On Leave DTO.
  Future<Map<String, dynamic>> submitOnLeave(OnLeaveDto dto, String s) {
    return _postWithMock<OnLeaveDto>(
      dto,
      '/dsractivity/onleave',
      successMockMessage: 'On Leave data submitted successfully (mock)',
    );
  }

  /// Submit Phone Call With Builder DTO.
  Future<Map<String, dynamic>> submitPhoneCallBuilder(PhoneCallBuilderDto dto) {
    return _postWithMock<PhoneCallBuilderDto>(
      dto,
      '/dsractivity/phonecallbuilder',
      successMockMessage: 'Phone Call Builder data submitted successfully (mock)',
    );
  }

  /// Submit Phone Call With Unregistered Purchaser DTO.
  Future<Map<String, dynamic>> submitPhoneCallUnregistered(PhoneCallUnregisteredDto dto) {
    return _postWithMock<PhoneCallUnregisteredDto>(
      dto,
      '/dsractivity/phonecallunregistered',
      successMockMessage: 'Phone Call Unregistered data submitted successfully (mock)',
    );
  }

  /// Fetch details for a given DSR ID.
  Future<Map<String, dynamic>> getDsrDetails(String dsrId) async {
    try {
      final response = await _dio.get('/api/dsr/$dsrId');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map<String, dynamic>) {
          return data;
        } else {
          return {'success': true, 'data': data};
        }
      }

      throw Exception('GET /api/dsr/$dsrId failed: HTTP ${response.statusCode}');
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.connectionTimeout) {
        return {
          'success': true,
          'data': {
            'dsrId': dsrId,
            'status': 'active',
            'details': 'Mock DSR details',
          },
        };
      }
      rethrow;
    }
  }

  // =====================================
  // 3) MOCK DATA PROVIDERS (for dev only)
  // =====================================

  List<AreaCodeDto> _getMockAreaCodes(String? search) {
    final mockData = [
      AreaCodeDto(areaCode: 'A001', areaName: 'North Region', areaType: 'Urban'),
      AreaCodeDto(areaCode: 'A002', areaName: 'South Region', areaType: 'Urban'),
      AreaCodeDto(areaCode: 'A003', areaName: 'East Region', areaType: 'Rural'),
      AreaCodeDto(areaCode: 'A004', areaName: 'West Region', areaType: 'Rural'),
      AreaCodeDto(areaCode: 'A005', areaName: 'Central Region', areaType: 'Mixed'),
    ];

    if (search == null || search.isEmpty) return mockData;

    final lower = search.toLowerCase();
    return mockData
        .where((a) =>
    a.areaCode.toLowerCase().contains(lower) ||
        (a.areaName?.toLowerCase().contains(lower) ?? false))
        .toList();
  }

  List<RetailerInfoDto> _getMockRetailers(
      String areaCode, String cusRtlFl, String? search) {
    final mockData = [
      RetailerInfoDto(
        code: 'R001',
        name: 'ABC Retailers',
        address: '123 Main St, City',
        latitude: 28.6139,
        longitude: 77.2090,
        kycStatus: 'Verified',
        contactPerson: 'John Doe',
        contactNumber: '9876543210',
      ),
      RetailerInfoDto(
        code: 'R002',
        name: 'XYZ Stores',
        address: '456 Park Ave, Town',
        latitude: 28.6129,
        longitude: 77.2080,
        kycStatus: 'Pending',
        contactPerson: 'Jane Smith',
        contactNumber: '8765432109',
      ),
      RetailerInfoDto(
        code: 'R003',
        name: 'PQR Enterprises',
        address: '789 Market Rd, Village',
        latitude: 28.6119,
        longitude: 77.2070,
        kycStatus: 'Verified',
        contactPerson: 'Bob Johnson',
        contactNumber: '7654321098',
      ),
    ];

    if (search == null || search.isEmpty) return mockData;

    final lower = search.toLowerCase();
    return mockData
        .where((r) =>
    (r.code?.toLowerCase().contains(lower) ?? false) ||
        (r.name?.toLowerCase().contains(lower) ?? false))
        .toList();
  }
}
