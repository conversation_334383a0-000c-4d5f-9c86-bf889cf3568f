// lib/models.dart

import 'dart:convert';

/// DTO for Area Code
class AreaCodeDto {
  final String areaCode;
  final String? areaName;
  final String? areaType;

  AreaCodeDto({
    required this.areaCode,
    this.areaName,
    this.areaType,
  });

  factory AreaCodeDto.fromJson(Map<String, dynamic> json) => AreaCodeDto(
    areaCode: json['areaCode'] as String,
    areaName: json['areaName'] as String?,
    areaType: json['areaType'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'areaCode': areaCode,
    if (areaName != null) 'areaName': areaName,
    if (areaType != null) 'areaType': areaType,
  };

  @override
  String toString() {
    // Display as "CODE – Name"
    if (areaName != null && areaName!.isNotEmpty) {
      return '$areaCode – $areaName';
    }
    return areaCode;
  }
}

/// DTO for Retailer / Customer Info
class RetailerInfoDto {
  final String? code;
  final String? name;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? kycStatus;
  final String? contactPerson;
  final String? contactNumber;

  RetailerInfoDto({
    this.code,
    this.name,
    this.address,
    this.latitude,
    this.longitude,
    this.kycStatus,
    this.contactPerson,
    this.contactNumber,
  });

  factory RetailerInfoDto.fromJson(Map<String, dynamic> json) => RetailerInfoDto(
    code: json['code'] as String?,
    name: json['name'] as String?,
    address: json['address'] as String?,
    latitude: (json['latitude'] as num?)?.toDouble(),
    longitude: (json['longitude'] as num?)?.toDouble(),
    kycStatus: json['kycStatus'] as String?,
    contactPerson: json['contactPerson'] as String?,
    contactNumber: json['contactNumber'] as String?,
  );

  Map<String, dynamic> toJson() => {
    if (code != null) 'code': code,
    if (name != null) 'name': name,
    if (address != null) 'address': address,
    if (latitude != null) 'latitude': latitude,
    if (longitude != null) 'longitude': longitude,
    if (kycStatus != null) 'kycStatus': kycStatus,
    if (contactPerson != null) 'contactPerson': contactPerson,
    if (contactNumber != null) 'contactNumber': contactNumber,
  };
}

/// DTO for submitting DSR header
class DsrHeaderSubmitDto {
  final String loginIdM;
  final String docuNumb;
  final String docuDate; // yyyy-MM-dd
  final String dsrParam;
  final String cusRtlFl; // 'R' or 'C'
  final String? areaCode;
  final String? cusRtlCd;
  final String? cusRtlNm;

  DsrHeaderSubmitDto({
    required this.loginIdM,
    required this.docuNumb,
    required this.docuDate,
    required this.dsrParam,
    required this.cusRtlFl,
    this.areaCode,
    this.cusRtlCd,
    this.cusRtlNm,
  });

  factory DsrHeaderSubmitDto.fromJson(Map<String, dynamic> json) => DsrHeaderSubmitDto(
    loginIdM: json['loginIdM'] as String,
    docuNumb: json['docuNumb'] as String,
    docuDate: json['docuDate'] as String,
    dsrParam: json['dsrParam'] as String,
    cusRtlFl: json['cusRtlFl'] as String,
    areaCode: json['areaCode'] as String?,
    cusRtlCd: json['cusRtlCd'] as String?,
    cusRtlNm: json['cusRtlNm'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'loginIdM': loginIdM,
    'docuNumb': docuNumb,
    'docuDate': docuDate,
    'dsrParam': dsrParam,
    'cusRtlFl': cusRtlFl,
    if (areaCode != null) 'areaCode': areaCode,
    if (cusRtlCd != null) 'cusRtlCd': cusRtlCd,
    if (cusRtlNm != null) 'cusRtlNm': cusRtlNm,
  };
}

/// Base class for “Activity” DTOs with common fields
abstract class ActivityBaseDto {
  final String proctype;       // e.g. "Add"/"Update"
  final String submdate;       // yyyy-MM-dd
  final String repodate;       // yyyy-MM-dd
  final String? actdetl1;
  final String? actdetl2;
  final String? actdetl3;
  final String? othrnote;
  final String? imgfirst;      // Base64 string
  final String? imgscndd;      // Base64 string
  final String? imgthird;      // Base64 string

  ActivityBaseDto({
    required this.proctype,
    required this.submdate,
    required this.repodate,
    this.actdetl1,
    this.actdetl2,
    this.actdetl3,
    this.othrnote,
    this.imgfirst,
    this.imgscndd,
    this.imgthird,
  });

  /// Converts common fields to a JSON map. Subclasses should call super.toJson() and add extra fields if needed.
  Map<String, dynamic> toJson() => {
    'proctype': proctype,
    'submdate': submdate,
    'repodate': repodate,
    if (actdetl1 != null) 'actdetl1': actdetl1,
    if (actdetl2 != null) 'actdetl2': actdetl2,
    if (actdetl3 != null) 'actdetl3': actdetl3,
    if (othrnote != null) 'othrnote': othrnote,
    if (imgfirst != null) 'imgfirst': imgfirst,
    if (imgscndd != null) 'imgscndd': imgscndd,
    if (imgthird != null) 'imgthird': imgthird,
  };
}

/// DTO for “Work From Home”
class WorkFromHomeDto extends ActivityBaseDto {
  WorkFromHomeDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory WorkFromHomeDto.fromJson(Map<String, dynamic> json) => WorkFromHomeDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Any Other Activity” (catch‐all)
class AnyOtherActivityDto extends ActivityBaseDto {
  AnyOtherActivityDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory AnyOtherActivityDto.fromJson(Map<String, dynamic> json) => AnyOtherActivityDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Full DSR” submission
class DsrActivityFullDto {
  final String loginIdM;
  final String docuNumb;
  final String proctype;
  final String docuDate;
  final String dsrParam;
  final String? cusRtlFl;
  final String? cusRtlCd;
  final String? areaCode;
  final String? cusRtlNm;
  final String? kycStatus;
  final String? marketName;
  final String? displayContestParticipation;
  final String? pendingIssueStatus;
  final String? pendingIssueDetailType;
  final String? pendingIssueSpecifyText;
  final String? enrolWCSlab;
  final String? enrolWCPSlab;
  final String? enrolVAPSlab;
  final String? bwStockWC;
  final String? bwStockWCP;
  final String? bwStockVAP;
  final String? brandsSellingWC;
  final String? wcIndustryVolume;
  final String? brandsSellingWCP;
  final String? wcpIndustryVolume;
  final String? last3MonthsBwWC;
  final String? last3MonthsBwWCP;
  final String? last3MonthsBwVAP;
  final String? currentMonthBwWC;
  final String? currentMonthBwWCP;
  final String? currentMonthBwVAP;
  final List<DsrCompetitorAvgSaleDto>? competitorSales;
  final List<DsrOrderItemDto>? orderItems;
  final List<DsrMarketWcpSkuDto>? marketWcpSkus;
  final String? orderExecDate;
  final String? otherRemarks;
  final List<DsrGiftItemDto>? giftItems;
  final String? isTileAdhesiveSeller;
  final String? tileAdhesiveStock;
  final String? geoLatit;
  final String? geoLongt;
  final String? purchaserLatit;
  final String? purchaserLongt;
  final String? locationCapturedAddress;
  final String? distanceExceptionReason;

  DsrActivityFullDto({
    required this.loginIdM,
    required this.docuNumb,
    required this.proctype,
    required this.docuDate,
    required this.dsrParam,
    this.cusRtlFl,
    this.cusRtlCd,
    this.areaCode,
    this.cusRtlNm,
    this.kycStatus,
    this.marketName,
    this.displayContestParticipation,
    this.pendingIssueStatus,
    this.pendingIssueDetailType,
    this.pendingIssueSpecifyText,
    this.enrolWCSlab,
    this.enrolWCPSlab,
    this.enrolVAPSlab,
    this.bwStockWC,
    this.bwStockWCP,
    this.bwStockVAP,
    this.brandsSellingWC,
    this.wcIndustryVolume,
    this.brandsSellingWCP,
    this.wcpIndustryVolume,
    this.last3MonthsBwWC,
    this.last3MonthsBwWCP,
    this.last3MonthsBwVAP,
    this.currentMonthBwWC,
    this.currentMonthBwWCP,
    this.currentMonthBwVAP,
    this.competitorSales,
    this.orderItems,
    this.marketWcpSkus,
    this.orderExecDate,
    this.otherRemarks,
    this.giftItems,
    this.isTileAdhesiveSeller,
    this.tileAdhesiveStock,
    this.geoLatit,
    this.geoLongt,
    this.purchaserLatit,
    this.purchaserLongt,
    this.locationCapturedAddress,
    this.distanceExceptionReason,
  });

  factory DsrActivityFullDto.fromJson(Map<String, dynamic> json) => DsrActivityFullDto(
    loginIdM: json['loginIdM'] as String,
    docuNumb: json['docuNumb'] as String,
    proctype: json['proctype'] as String,
    docuDate: json['docuDate'] as String,
    dsrParam: json['dsrParam'] as String,
    cusRtlFl: json['cusRtlFl'] as String?,
    cusRtlCd: json['cusRtlCd'] as String?,
    areaCode: json['areaCode'] as String?,
    cusRtlNm: json['cusRtlNm'] as String?,
    kycStatus: json['kycStatus'] as String?,
    marketName: json['marketName'] as String?,
    displayContestParticipation: json['displayContestParticipation'] as String?,
    pendingIssueStatus: json['pendingIssueStatus'] as String?,
    pendingIssueDetailType: json['pendingIssueDetailType'] as String?,
    pendingIssueSpecifyText: json['pendingIssueSpecifyText'] as String?,
    enrolWCSlab: json['enrolWCSlab'] as String?,
    enrolWCPSlab: json['enrolWCPSlab'] as String?,
    enrolVAPSlab: json['enrolVAPSlab'] as String?,
    bwStockWC: json['bwStockWC'] as String?,
    bwStockWCP: json['bwStockWCP'] as String?,
    bwStockVAP: json['bwStockVAP'] as String?,
    brandsSellingWC: json['brandsSellingWC'] as String?,
    wcIndustryVolume: json['wcIndustryVolume'] as String?,
    brandsSellingWCP: json['brandsSellingWCP'] as String?,
    wcpIndustryVolume: json['wcpIndustryVolume'] as String?,
    last3MonthsBwWC: json['last3MonthsBwWC'] as String?,
    last3MonthsBwWCP: json['last3MonthsBwWCP'] as String?,
    last3MonthsBwVAP: json['last3MonthsBwVAP'] as String?,
    currentMonthBwWC: json['currentMonthBwWC'] as String?,
    currentMonthBwWCP: json['currentMonthBwWCP'] as String?,
    currentMonthBwVAP: json['currentMonthBwVAP'] as String?,
    competitorSales: (json['competitorSales'] as List<dynamic>?)
        ?.map((e) => DsrCompetitorAvgSaleDto.fromJson(e as Map<String, dynamic>))
        .toList(),
    orderItems: (json['orderItems'] as List<dynamic>?)
        ?.map((e) => DsrOrderItemDto.fromJson(e as Map<String, dynamic>))
        .toList(),
    marketWcpSkus: (json['marketWcpSkus'] as List<dynamic>?)
        ?.map((e) => DsrMarketWcpSkuDto.fromJson(e as Map<String, dynamic>))
        .toList(),
    orderExecDate: json['orderExecDate'] as String?,
    otherRemarks: json['otherRemarks'] as String?,
    giftItems: (json['giftItems'] as List<dynamic>?)
        ?.map((e) => DsrGiftItemDto.fromJson(e as Map<String, dynamic>))
        .toList(),
    isTileAdhesiveSeller: json['isTileAdhesiveSeller'] as String?,
    tileAdhesiveStock: json['tileAdhesiveStock'] as String?,
    geoLatit: json['geoLatit'] as String?,
    geoLongt: json['geoLongt'] as String?,
    purchaserLatit: json['purchaserLatit'] as String?,
    purchaserLongt: json['purchaserLongt'] as String?,
    locationCapturedAddress: json['locationCapturedAddress'] as String?,
    distanceExceptionReason: json['distanceExceptionReason'] as String?,
  );

  Map<String, dynamic> toJson() {
    return {
      'loginIdM': loginIdM,
      'docuNumb': docuNumb,
      'proctype': proctype,
      'docuDate': docuDate,
      'dsrParam': dsrParam,
      if (cusRtlFl != null) 'cusRtlFl': cusRtlFl,
      if (cusRtlCd != null) 'cusRtlCd': cusRtlCd,
      if (areaCode != null) 'areaCode': areaCode,
      if (cusRtlNm != null) 'cusRtlNm': cusRtlNm,
      if (kycStatus != null) 'kycStatus': kycStatus,
      if (marketName != null) 'marketName': marketName,
      if (displayContestParticipation != null) 'displayContestParticipation': displayContestParticipation,
      if (pendingIssueStatus != null) 'pendingIssueStatus': pendingIssueStatus,
      if (pendingIssueDetailType != null) 'pendingIssueDetailType': pendingIssueDetailType,
      if (pendingIssueSpecifyText != null) 'pendingIssueSpecifyText': pendingIssueSpecifyText,
      if (enrolWCSlab != null) 'enrolWCSlab': enrolWCSlab,
      if (enrolWCPSlab != null) 'enrolWCPSlab': enrolWCPSlab,
      if (enrolVAPSlab != null) 'enrolVAPSlab': enrolVAPSlab,
      if (bwStockWC != null) 'bwStockWC': bwStockWC,
      if (bwStockWCP != null) 'bwStockWCP': bwStockWCP,
      if (bwStockVAP != null) 'bwStockVAP': bwStockVAP,
      if (brandsSellingWC != null) 'brandsSellingWC': brandsSellingWC,
      if (wcIndustryVolume != null) 'wcIndustryVolume': wcIndustryVolume,
      if (brandsSellingWCP != null) 'brandsSellingWCP': brandsSellingWCP,
      if (wcpIndustryVolume != null) 'wcpIndustryVolume': wcpIndustryVolume,
      if (last3MonthsBwWC != null) 'last3MonthsBwWC': last3MonthsBwWC,
      if (last3MonthsBwWCP != null) 'last3MonthsBwWCP': last3MonthsBwWCP,
      if (last3MonthsBwVAP != null) 'last3MonthsBwVAP': last3MonthsBwVAP,
      if (currentMonthBwWC != null) 'currentMonthBwWC': currentMonthBwWC,
      if (currentMonthBwWCP != null) 'currentMonthBwWCP': currentMonthBwWCP,
      if (currentMonthBwVAP != null) 'currentMonthBwVAP': currentMonthBwVAP,
      if (competitorSales != null)
        'competitorSales': competitorSales!.map((e) => e.toJson()).toList(),
      if (orderItems != null)
        'orderItems': orderItems!.map((e) => e.toJson()).toList(),
      if (marketWcpSkus != null)
        'marketWcpSkus': marketWcpSkus!.map((e) => e.toJson()).toList(),
      if (orderExecDate != null) 'orderExecDate': orderExecDate,
      if (otherRemarks != null) 'otherRemarks': otherRemarks,
      if (giftItems != null)
        'giftItems': giftItems!.map((e) => e.toJson()).toList(),
      if (isTileAdhesiveSeller != null) 'isTileAdhesiveSeller': isTileAdhesiveSeller,
      if (tileAdhesiveStock != null) 'tileAdhesiveStock': tileAdhesiveStock,
      if (geoLatit != null) 'geoLatit': geoLatit,
      if (geoLongt != null) 'geoLongt': geoLongt,
      if (purchaserLatit != null) 'purchaserLatit': purchaserLatit,
      if (purchaserLongt != null) 'purchaserLongt': purchaserLongt,
      if (locationCapturedAddress != null) 'locationCapturedAddress': locationCapturedAddress,
      if (distanceExceptionReason != null) 'distanceExceptionReason': distanceExceptionReason,
    };
  }
}

/// DTO for Competitor Average Sale row
class DsrCompetitorAvgSaleDto {
  final String? competitorName;
  final String? wcQty;
  final String? wcpQty;

  DsrCompetitorAvgSaleDto({
    this.competitorName,
    this.wcQty,
    this.wcpQty,
  });

  factory DsrCompetitorAvgSaleDto.fromJson(Map<String, dynamic> json) => DsrCompetitorAvgSaleDto(
    competitorName: json['competitorName'] as String?,
    wcQty: json['wcQty'] as String?,
    wcpQty: json['wcpQty'] as String?,
  );

  Map<String, dynamic> toJson() => {
    if (competitorName != null) 'competitorName': competitorName,
    if (wcQty != null) 'wcQty': wcQty,
    if (wcpQty != null) 'wcpQty': wcpQty,
  };
}

/// DTO for Order Item row
class DsrOrderItemDto {
  final String? repoCatg;
  final String? prodCode;
  final int? quantityInBags;
  final double? quantityInMT;

  DsrOrderItemDto({
    this.repoCatg,
    this.prodCode,
    this.quantityInBags,
    this.quantityInMT,
  });

  factory DsrOrderItemDto.fromJson(Map<String, dynamic> json) => DsrOrderItemDto(
    repoCatg: json['repoCatg'] as String?,
    prodCode: json['prodCode'] as String?,
    quantityInBags: json['quantityInBags'] as int?,
    quantityInMT: (json['quantityInMT'] as num?)?.toDouble(),
  );

  Map<String, dynamic> toJson() => {
    if (repoCatg != null) 'repoCatg': repoCatg,
    if (prodCode != null) 'prodCode': prodCode,
    if (quantityInBags != null) 'quantityInBags': quantityInBags,
    if (quantityInMT != null) 'quantityInMT': quantityInMT,
  };
}

/// DTO for Gift Item row
class DsrGiftItemDto {
  final String? giftTypeCode;
  final int? quantity;

  DsrGiftItemDto({
    this.giftTypeCode,
    this.quantity,
  });

  factory DsrGiftItemDto.fromJson(Map<String, dynamic> json) => DsrGiftItemDto(
    giftTypeCode: json['giftTypeCode'] as String?,
    quantity: json['quantity'] as int?,
  );

  Map<String, dynamic> toJson() => {
    if (giftTypeCode != null) 'giftTypeCode': giftTypeCode,
    if (quantity != null) 'quantity': quantity,
  };
}

/// DTO for Market WCP SKU row
class DsrMarketWcpSkuDto {
  final String? brandName;
  final String? prdCodMk;
  final String? bPriceVl;
  final String? cPriceVl;

  DsrMarketWcpSkuDto({
    this.brandName,
    this.prdCodMk,
    this.bPriceVl,
    this.cPriceVl,
  });

  factory DsrMarketWcpSkuDto.fromJson(Map<String, dynamic> json) => DsrMarketWcpSkuDto(
    brandName: json['brandName'] as String?,
    prdCodMk: json['prdCodMk'] as String?,
    bPriceVl: json['bPriceVl'] as String?,
    cPriceVl: json['cPriceVl'] as String?,
  );

  Map<String, dynamic> toJson() => {
    if (brandName != null) 'brandName': brandName,
    if (prdCodMk != null) 'prdCodMk': prdCodMk,
    if (bPriceVl != null) 'bPriceVl': bPriceVl,
    if (cPriceVl != null) 'cPriceVl': cPriceVl,
  };
}

/// DTO for BTL Activity
class BtlActivityDto extends ActivityBaseDto {
  BtlActivityDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory BtlActivityDto.fromJson(Map<String, dynamic> json) => BtlActivityDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for Check Sampling
class CheckSamplingDto extends ActivityBaseDto {
  CheckSamplingDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory CheckSamplingDto.fromJson(Map<String, dynamic> json) => CheckSamplingDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Meeting With New Purchaser”
class MeetingNewPurchaserDto extends ActivityBaseDto {
  MeetingNewPurchaserDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory MeetingNewPurchaserDto.fromJson(Map<String, dynamic> json) => MeetingNewPurchaserDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Meeting With Contractor”
class MeetingContractorDto extends ActivityBaseDto {
  MeetingContractorDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory MeetingContractorDto.fromJson(Map<String, dynamic> json) => MeetingContractorDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Internal Team Meeting”
class InternalTeamMeetingDto extends ActivityBaseDto {
  InternalTeamMeetingDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory InternalTeamMeetingDto.fromJson(Map<String, dynamic> json) => InternalTeamMeetingDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Office Work”
class OfficeWorkDto extends ActivityBaseDto {
  OfficeWorkDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory OfficeWorkDto.fromJson(Map<String, dynamic> json) => OfficeWorkDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “On Leave”
class OnLeaveDto extends ActivityBaseDto {
  OnLeaveDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory OnLeaveDto.fromJson(Map<String, dynamic> json) => OnLeaveDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Phone Call With Builder”
class PhoneCallBuilderDto extends ActivityBaseDto {
  PhoneCallBuilderDto({
    required String proctype,
    required String submdate,
    required String repodate,
    String? actdetl1,
    String? actdetl2,
    String? actdetl3,
    String? othrnote,
    String? imgfirst,
    String? imgscndd,
    String? imgthird,
  }) : super(
    proctype: proctype,
    submdate: submdate,
    repodate: repodate,
    actdetl1: actdetl1,
    actdetl2: actdetl2,
    actdetl3: actdetl3,
    othrnote: othrnote,
    imgfirst: imgfirst,
    imgscndd: imgscndd,
    imgthird: imgthird,
  );

  factory PhoneCallBuilderDto.fromJson(Map<String, dynamic> json) => PhoneCallBuilderDto(
    proctype: json['proctype'] as String,
    submdate: json['submdate'] as String,
    repodate: json['repodate'] as String,
    actdetl1: json['actdetl1'] as String?,
    actdetl2: json['actdetl2'] as String?,
    actdetl3: json['actdetl3'] as String?,
    othrnote: json['othrnote'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );
}

/// DTO for “Phone Call With Unregistered Purchaser”
class PhoneCallUnregisteredDto {
  final String processType;
  final String submissionDate; // yyyy-MM-dd
  final String reportDate;     // yyyy-MM-dd
  final String? purchdet;
  final String? topdisc;
  final String? remarksf;
  final String? imgfirst;
  final String? imgscndd;
  final String? imgthird;

  PhoneCallUnregisteredDto({
    required this.processType,
    required this.submissionDate,
    required this.reportDate,
    this.purchdet,
    this.topdisc,
    this.remarksf,
    this.imgfirst,
    this.imgscndd,
    this.imgthird,
  });

  factory PhoneCallUnregisteredDto.fromJson(Map<String, dynamic> json) => PhoneCallUnregisteredDto(
    processType: json['processType'] as String,
    submissionDate: json['submissionDate'] as String,
    reportDate: json['reportDate'] as String,
    purchdet: json['purchdet'] as String?,
    topdisc: json['topdisc'] as String?,
    remarksf: json['remarksf'] as String?,
    imgfirst: json['imgfirst'] as String?,
    imgscndd: json['imgscndd'] as String?,
    imgthird: json['imgthird'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'processType': processType,
    'submissionDate': submissionDate,
    'reportDate': reportDate,
    if (purchdet != null) 'purchdet': purchdet,
    if (topdisc != null) 'topdisc': topdisc,
    if (remarksf != null) 'remarksf': remarksf,
    if (imgfirst != null) 'imgfirst': imgfirst,
    if (imgscndd != null) 'imgscndd': imgscndd,
    if (imgthird != null) 'imgthird': imgthird,
  };
}
