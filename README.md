# Token Scanner App

A Flutter application for scanning and validating tokens using QR codes.

## Features

- QR Code scanning with real-time camera feed
- PIN validation system
- Token history tracking
- Torch control
- Error handling and retry mechanisms
- Beautiful and intuitive UI

## Getting Started

### Prerequisites

- Flutter SDK
- Android Studio / VS Code
- Android SDK / Xcode (for iOS)

### Installation

1. Clone the repository:
```bash
git clone [repository-url]
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
flutter run
```

## Dependencies

- mobile_scanner: For QR code scanning
- http: For API calls
- flutter/material.dart: For UI components

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request
