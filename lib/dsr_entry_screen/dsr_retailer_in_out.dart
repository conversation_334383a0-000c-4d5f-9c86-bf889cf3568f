// Removed unused import
// import 'dart:math'; // No longer needed if API generates DocuNumb
import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
// import 'package:http/http.dart' as http; // Using ApiService with Dio

import 'DsrVisit.dart'; // Assuming DsrVisit.dart is in the same directory
import '../utils/distance_calculator.dart'; // Assuming this path is correct
import '../models.dart';
import '../api_service.dart';
import '../theme/app_theme.dart'; // Assuming your AppTheme is here

class DsrRetailerInOut extends StatefulWidget {
  const DsrRetailerInOut({super.key});

  @override
  State<DsrRetailerInOut> createState() => _DsrRetailerInOutState();
}

class _DsrRetailerInOutState extends State<DsrRetailerInOut>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final ApiService _apiService = ApiService();

  String? _purchaserRetailerItem = 'Select'; // For "Purchaser / Retailer Type" Dropdown
  final List<String> _purchaserRetailerDropdownItems = [ // Options from JSP
    'Select', 'Retailer', 'Rural Retailer', 'Stokiest/Urban Stokiest',
    'Direct Dealer', 'Rural Stokiest', 'AD', 'UBS'
  ];
  AreaCodeDto? _selectedAreaCodeDto;
  RetailerInfoDto? _selectedRetailerInfoDto;

  DateTime? _selectedDate;

  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _yourLatitudeController = TextEditingController();
  final TextEditingController _yourLongitudeController = TextEditingController();
  final TextEditingController _custLatitudeController = TextEditingController();
  final TextEditingController _custLongitudeController = TextEditingController();
  final TextEditingController _customerNameController = TextEditingController(); // Displays selected retailer name
  // _codeSearchController is effectively replaced by _selectedRetailerInfoDto.code

  final _formKey = GlobalKey<FormState>();

  final Color _primaryColor = AppTheme.primaryColor; // Using AppTheme
  final Color _secondaryColor = Colors.blueAccent; // Example
  final Color _backgroundColor = AppTheme.scaffoldBackgroundColor;
  final Color _cardColor = AppTheme.cardColor;
  final Color _textColor = AppTheme.textColor;
  // Removed unused field

  double? _calculatedDistance;
  bool _isCalculatingDistance = false;
  bool _isSubmitting = false;


  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();

    _selectedDate = DateTime.now();
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);
    // _apiService.currentLoginIdM = "your_actual_login_id"; // IMPORTANT: Set this from auth
  }

  @override
  void dispose() {
    _animationController.dispose();
    _dateController.dispose();
    _yourLatitudeController.dispose();
    _yourLongitudeController.dispose();
    _custLatitudeController.dispose();
    _custLongitudeController.dispose();
    _customerNameController.dispose();
    super.dispose();
  }

  Future<List<AreaCodeDto>> _fetchAreaCodes(String? filter) async {
    try {
      return await _apiService.getAreaCodes(search: filter);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load area codes: $e'), backgroundColor: Colors.red),
        );
      }
      return [];
    }
  }

  Future<List<RetailerInfoDto>> _fetchRetailers(String? filter) async {
    if (_selectedAreaCodeDto == null || _purchaserRetailerItem == 'Select' || _purchaserRetailerItem == null) {
      return []; // Don't fetch if prerequisites are not met
    }
    try {
      return await _apiService.getRetailers(
        areaCode: _selectedAreaCodeDto!.areaCode,
        cusRtlFl: _purchaserRetailerItem!,
        search: filter,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load retailers: $e'), backgroundColor: Colors.red),
        );
      }
      return [];
    }
  }

  Future<Position> _determinePosition() async {
    // ... (Geolocator logic remains the same)
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled. Please enable them.');
    }
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied. Please grant permissions in settings.');
      }
    }
    if (permission == LocationPermission.deniedForever) {
      return Future.error('Location permissions are permanently denied. Please enable them in app settings.');
    }
    return await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
  }

  Future<void> _captureYourLocation() async {
    try {
      final pos = await _determinePosition();
      setState(() {
        _yourLatitudeController.text = pos.latitude.toStringAsFixed(6);
        _yourLongitudeController.text = pos.longitude.toStringAsFixed(6);
      });
      _calculateDistance();
    } catch (e) {
      _showError(e.toString());
    }
  }

  Future<void> _captureCustomerLocation() async {
    // This button might be redundant if retailer master data is accurate.
    // If used, it would manually set customer's lat/long.
    // If _selectedRetailerInfoDto has lat/long, this might override it for this session.
    try {
      final pos = await _determinePosition();
      setState(() {
        _custLatitudeController.text = pos.latitude.toStringAsFixed(6);
        _custLongitudeController.text = pos.longitude.toStringAsFixed(6);
      });
      _calculateDistance(); // Recalculate with new manually captured customer location
    } catch (e) {
      _showError(e.toString());
    }
  }

  void _showError(String msg) {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Error'), content: Text(msg),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
      ),
    );
  }

  void _populateRetailerData(RetailerInfoDto retailer) {
    setState(() {
      _selectedRetailerInfoDto = retailer;
      _customerNameController.text = retailer.name ?? '';
      _custLatitudeController.text = retailer.latitude?.toStringAsFixed(6) ?? '';
      _custLongitudeController.text = retailer.longitude?.toStringAsFixed(6) ?? '';
    });
    _calculateDistance();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Selected: ${retailer.code} - ${retailer.name}'),
          backgroundColor: Colors.green.shade700, behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _calculateDistance() {
    if (_yourLatitudeController.text.isEmpty || _yourLongitudeController.text.isEmpty ||
        _custLatitudeController.text.isEmpty || _custLongitudeController.text.isEmpty) {
      setState(() => _calculatedDistance = null);
      return;
    }
    try {
      setState(() => _isCalculatingDistance = true);
      final userLat = double.parse(_yourLatitudeController.text);
      final userLon = double.parse(_yourLongitudeController.text);
      final retailerLat = double.parse(_custLatitudeController.text);
      final retailerLon = double.parse(_custLongitudeController.text);
      final distance = DistanceCalculator.calculateDistance(userLat, userLon, retailerLat, retailerLon);
      setState(() {
        _calculatedDistance = distance;
        _isCalculatingDistance = false;
      });
    } catch (e) {
      setState(() {
        _calculatedDistance = null;
        _isCalculatingDistance = false;
      });
      debugPrint('Error calculating distance: $e');
    }
  }

  Future<void> _pickDate() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? now,
      firstDate: DateTime(2000),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!), // Use AppTheme helper
    );
    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<String?> _showDistanceExceptionDialog(double distance) async {
    String? reason;
    final reasonController = TextEditingController();
    final reasonFormKey = GlobalKey<FormState>();

    return showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(children: [
          Icon(Icons.report_problem_outlined, color: Colors.orange.shade700, size: 24),
          const SizedBox(width: 8),
          Expanded(child: Text('Distance Exception', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.orange.shade800))),
        ]),
        content: SingleChildScrollView(
          child: Form(
            key: reasonFormKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your current distance is ${DistanceCalculator.formatDistance(distance)}, which is outside the allowed ${DistanceCalculator.checkInRangeMeters}m range.',
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade700, height: 1.4),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: reasonController,
                  decoration: AppTheme.textFieldInputDecoration(hintText: 'e.g., GPS inaccurate at site').copyWith(labelText: 'Reason for deviation'),
                  validator: (value) => (value == null || value.trim().isEmpty) ? 'Reason is required for out-of-range entry.' : null,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(child: const Text('Cancel'), onPressed: () => Navigator.of(context).pop(null)),
          ElevatedButton(
            child: const Text('Submit with Reason'),
            style: AppTheme.elevatedButtonStyle(),
            onPressed: () {
              if (reasonFormKey.currentState!.validate()) {
                Navigator.of(context).pop(reasonController.text.trim());
              }
            },
          ),
        ],
      ),
    );
  }


  Future<void> _submitToServer(String entryType) async {
    if (!_formKey.currentState!.validate()) return;
    if (_purchaserRetailerItem == 'Select' || _selectedAreaCodeDto == null || _selectedRetailerInfoDto == null) {
      _showError('Please select Purchaser Type, Area Code, and a Retailer.');
      return;
    }
    setState(() => _isSubmitting = true);
    String? distanceExceptionReason;

    // JSP logic: dsrParam '05' for Personal Visit (IN), '04' for Tele Call (OUT/Exception)
    String dsrParamForApi = (entryType == "IN") ? "05" : "04";

    if (entryType == "IN") {
      if (_yourLatitudeController.text.isEmpty || _yourLongitudeController.text.isEmpty) {
        _showError('Please capture your current location for IN entry.');
        setState(() => _isSubmitting = false);
        return;
      }
      if (_calculatedDistance == null) _calculateDistance();
      if (_calculatedDistance == null) {
        _showError('Could not calculate distance. Ensure locations are captured.');
        setState(() => _isSubmitting = false);
        return;
      }
      if (!DistanceCalculator.isWithinCheckInRange(_calculatedDistance!)) {
        distanceExceptionReason = await _showDistanceExceptionDialog(_calculatedDistance!);
        if (distanceExceptionReason == null || distanceExceptionReason.isEmpty) {
          setState(() => _isSubmitting = false);
          return;
        }
      }
    }

    final dto = DsrHeaderSubmitDto(
      loginIdM: _apiService.currentLoginIdM,
      docuDate: _dateController.text,
      dsrParam: dsrParamForApi,
      cusRtlFl: _purchaserRetailerItem!,
      cusRtlCd: _selectedRetailerInfoDto!.code!,
      areaCode: _selectedAreaCodeDto!.areaCode,
      // cuRtType could map to something from RetailerInfoDto if needed by API for header
      cuRtType: _selectedRetailerInfoDto!.kycStatus, // Example, adjust if API needs different mapping
      geoLatit: _yourLatitudeController.text.isNotEmpty ? _yourLatitudeController.text : null,
      geoLongt: _yourLongitudeController.text.isNotEmpty ? _yourLongitudeController.text : null,
      distance: _calculatedDistance,
      distanceExceptionReason: distanceExceptionReason,
    );

    try {
      final result = await _apiService.submitDsrHeader(dto);
      final generatedDocuNumb = result['generatedDocuNumb'] as String?;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("DSR Header submitted. Document No: ${generatedDocuNumb ?? 'N/A'}"),
            backgroundColor: Colors.green, behavior: SnackBarBehavior.floating,
          ),
        );
      }

      if (entryType == "IN" && generatedDocuNumb != null && mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => DsrVisit(
            docuNumb: generatedDocuNumb,
            areaCode: dto.areaCode ?? '',
            retailerCode: dto.cusRtlCd ?? '',
            retailerName: _selectedRetailerInfoDto?.name ?? dto.cusRtlCd ?? '',
            loginId: _apiService.currentLoginIdM,
            // Pass initial Geo data if available and needed by DsrVisit
            initialGeoLatit: dto.geoLatit,
            initialGeoLongt: dto.geoLongt,
            initialPurchaserLatit: _selectedRetailerInfoDto?.latitude?.toStringAsFixed(6),
            initialPurchaserLongt: _selectedRetailerInfoDto?.longitude?.toStringAsFixed(6),
          )),
        );
      } else if (entryType == "OUT" && mounted) {
        // For "OUT" or "Exception", perhaps just pop or go to DSR list
        Navigator.of(context).pop();
      }
      // _clearAllFields(); // Optionally clear form if not navigating away
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Submission failed: $e"), backgroundColor: Colors.red, behavior: SnackBarBehavior.floating),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _clearAllFields() {
    _formKey.currentState?.reset();
    setState(() {
      _purchaserRetailerItem = 'Select';
      _selectedAreaCodeDto = null;
      _selectedRetailerInfoDto = null;
      _selectedDate = DateTime.now();
      _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      _yourLatitudeController.clear();
      _yourLongitudeController.clear();
      _custLatitudeController.clear();
      _custLongitudeController.clear();
      _customerNameController.clear();
      _calculatedDistance = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: AppBar(
        leading: IconButton(icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white), onPressed: () => Navigator.pop(context)),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('DSR Retailer IN OUT', style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold)),
            Text('Daily Sales Report Entry', style: TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 14)),
          ],
        ),
        actions: [ IconButton(icon: const Icon(Icons.help_outline, color: Colors.white), onPressed: () {/* Help action */})],
        backgroundColor: _primaryColor, elevation: 0,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(bottom: Radius.circular(15))),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildCard(title: 'Purchaser / Retailer Type', child: DropdownButtonFormField<String>(
                  value: _purchaserRetailerItem,
                  decoration: AppTheme.dropdownDecoration(hintText: 'Select Type'), // Use theme
                  items: _purchaserRetailerDropdownItems.map((item) => DropdownMenuItem<String>(value: item, child: Text(item))).toList(),
                  onChanged: (val) => setState(() {
                    _purchaserRetailerItem = val;
                    _selectedRetailerInfoDto = null;
                    _customerNameController.clear();
                    _custLatitudeController.clear(); _custLongitudeController.clear();
                    _calculatedDistance = null;
                  }),
                  validator: (value) => (value == null || value == 'Select') ? 'Please select a type' : null,
                ),
                ),
                _buildCard(title: 'Area Code', child: DropdownSearch<AreaCodeDto>(
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    searchFieldProps: TextFieldProps(decoration: AppTheme.textFieldInputDecoration(hintText: 'Search area code...').copyWith(prefixIcon: Icon(Icons.search, color: _secondaryColor))),
                    itemBuilder: (context, item, isSelected) => ListTile(title: Text(item.toString())),
                    emptyBuilder: (context, searchEntry) => const Center(child: Padding(padding: EdgeInsets.all(12.0), child: Text('No areas found or type to search.'))),
                  ),
                  dropdownDecoratorProps: DropDownDecoratorProps(dropdownSearchDecoration: AppTheme.dropdownDecoration(hintText: 'Select Area Code')),
                  asyncItems: (String? filter) => _fetchAreaCodes(filter),
                  onChanged: (AreaCodeDto? val) {
                    if (val != null) {
                      setState(() {
                        _selectedAreaCodeDto = val;
                        _selectedRetailerInfoDto = null;
                        _customerNameController.clear();
                        _custLatitudeController.clear(); _custLongitudeController.clear();
                        _calculatedDistance = null;
                      });
                    }
                  },
                  selectedItem: _selectedAreaCodeDto,
                  validator: (value) => (value == null) ? 'Please select an area' : null,
                  itemAsString: (AreaCodeDto u) => u.toString(), // Uses AreaCodeDto.toString()
                  compareFn: (i, s) => i.areaCode == s.areaCode,
                ),
                ),
                _buildCard(title: 'Retailer Search', child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DropdownSearch<RetailerInfoDto>(
                      popupProps: PopupProps.menu(
                        showSearchBox: true,
                        searchFieldProps: TextFieldProps(decoration: AppTheme.textFieldInputDecoration(hintText: 'Search retailer...').copyWith(prefixIcon: Icon(Icons.search, color: _secondaryColor))),
                        itemBuilder: (context, item, isSelected) => ListTile(title: Text(item.toString()), subtitle: Text(item.address ?? '')),
                        emptyBuilder: (context, searchEntry) => const Center(child: Padding(padding: EdgeInsets.all(12.0), child: Text('No retailers found. Select Area/Type or broaden search.'))),
                      ),
                      dropdownDecoratorProps: DropDownDecoratorProps(dropdownSearchDecoration: AppTheme.dropdownDecoration(hintText: 'Select Retailer')),
                      asyncItems: (String? filter) => _fetchRetailers(filter),
                      onChanged: (RetailerInfoDto? val) { if (val != null) _populateRetailerData(val);},
                      selectedItem: _selectedRetailerInfoDto,
                      validator: (value) => (value == null) ? 'Please select a retailer' : null,
                      itemAsString: (RetailerInfoDto u) => u.toString(),
                      compareFn: (i, s) => i.code == s.code,
                    ),
                  ],
                ),
                ),
                _buildCard(title: 'Customer Details', child: TextFormField(
                  controller: _customerNameController,
                  readOnly: true,
                  decoration: AppTheme.textFieldInputDecoration(hintText: 'Customer Name (auto-filled)'),
                  validator: (value) => (value == null || value.isEmpty) ? 'Please select a retailer' : null,
                ),
                ),
                _buildCard(title: 'Date', child: TextFormField(
                  controller: _dateController,
                  readOnly: true,
                  decoration: AppTheme.textFieldInputDecoration(hintText: 'Select Date').copyWith(
                    suffixIcon: IconButton(icon: Icon(Icons.calendar_today, color: _primaryColor), onPressed: _pickDate),
                  ),
                  validator: (value) => (value == null || value.isEmpty) ? 'Please select a date' : null,
                ),
                ),
                _buildCard(title: 'Location Details', child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppTheme.buildLabel('Your Location'), const SizedBox(height: 8),
                    Row(children: [
                      Expanded(child: TextFormField(controller: _yourLatitudeController, readOnly: true, decoration: AppTheme.textFieldInputDecoration(hintText: 'Latitude'))), const SizedBox(width: 8),
                      Expanded(child: TextFormField(controller: _yourLongitudeController, readOnly: true, decoration: AppTheme.textFieldInputDecoration(hintText: 'Longitude'))),
                    ]), const SizedBox(height: 8),
                    _buildElevatedButton(icon: Icons.my_location, label: 'Capture Your Location', onPressed: _captureYourLocation),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Customer Location'), const SizedBox(height: 8),
                    Row(children: [
                      Expanded(child: TextFormField(controller: _custLatitudeController, readOnly: true, decoration: AppTheme.textFieldInputDecoration(hintText: 'Latitude (from master)'))), const SizedBox(width: 8),
                      Expanded(child: TextFormField(controller: _custLongitudeController, readOnly: true, decoration: AppTheme.textFieldInputDecoration(hintText: 'Longitude (from master)'))),
                    ]),
                    // const SizedBox(height: 8),
                    // _buildElevatedButton(icon: Icons.location_on, label: 'Recapture Customer Location', onPressed: _captureCustomerLocation),
                    const SizedBox(height: 16),
                    if (_isCalculatingDistance || _calculatedDistance != null)
                      Container( /* ... Distance Display UI remains same ... */
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _isCalculatingDistance ? Colors.blue.shade50 : DistanceCalculator.isWithinCheckInRange(_calculatedDistance ?? 0) ? Colors.green.shade50 : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: _isCalculatingDistance ? Colors.blue.shade200 : DistanceCalculator.isWithinCheckInRange(_calculatedDistance ?? 0) ? Colors.green.shade200 : Colors.red.shade200),
                        ),
                        child: Row(children: [
                          if (_isCalculatingDistance) SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600)))
                          else Icon(DistanceCalculator.isWithinCheckInRange(_calculatedDistance!) ? Icons.check_circle : Icons.error, color: DistanceCalculator.isWithinCheckInRange(_calculatedDistance!) ? Colors.green.shade700 : Colors.red.shade700, size: 20),
                          const SizedBox(width: 8),
                          Expanded(child: Text(_isCalculatingDistance ? 'Calculating distance...' : 'Distance: ${DistanceCalculator.formatDistance(_calculatedDistance!)}', style: TextStyle(fontWeight: FontWeight.w600, color: _isCalculatingDistance ? Colors.blue.shade700 : DistanceCalculator.isWithinCheckInRange(_calculatedDistance!) ? Colors.green.shade700 : Colors.red.shade700))),
                          if (!_isCalculatingDistance && _calculatedDistance != null) Text(DistanceCalculator.isWithinCheckInRange(_calculatedDistance!) ? '✓ Within range' : '✗ Out of range', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: DistanceCalculator.isWithinCheckInRange(_calculatedDistance!) ? Colors.green.shade600 : Colors.red.shade600)),
                        ]),
                      ),
                  ],
                ),
                ),
                const SizedBox(height: 20),
                Row(children: [
                  Expanded(child: _buildActionButton(label: 'IN', color: _primaryColor, onPressed: _isSubmitting ? null : () => _submitToServer("IN"))),
                  const SizedBox(width: 16),
                  Expanded(child: _buildActionButton(label: 'OUT (Tele-Call)', color: Colors.orange.shade700, onPressed: _isSubmitting ? null : () => _submitToServer("OUT"))),
                ]),
                if (_isSubmitting) const Padding(padding: EdgeInsets.symmetric(vertical: 16.0), child: Center(child: CircularProgressIndicator())),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCard({required String title, required Widget child}) {
    return Card(
      elevation: 2, margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: _cardColor,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: _textColor)),
            const SizedBox(height: 12),
            child,
          ],
        ),
      ),
    );
  }

  // Removed unused method _inputDecoration

  Widget _buildElevatedButton({required IconData icon, required String label, VoidCallback? onPressed}) {
    // Using AppTheme's helper now
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: AppTheme.elevatedButtonStyle(bgColor: _secondaryColor),
    );
  }

  Widget _buildActionButton({required String label, required Color color, VoidCallback? onPressed}) {
    // Using AppTheme's helper now
    return ElevatedButton(
      onPressed: onPressed,
      style: AppTheme.elevatedButtonStyle(bgColor: color),
      child: Text(label, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
    );
  }

  void _showManualSearchPopup() {
    _showError("Manual search is not fully implemented. Please use the autocomplete search.");
  }
}