import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dropdown_search/dropdown_search.dart'; // Assuming this is still used from DsrRetailerInOut

import '../theme/app_theme.dart';
import 'dsr_entry.dart';
import '../models.dart'; // Import your models
import '../api_service.dart'; // Import your ApiService

class MeetingsWithContractor extends StatefulWidget {
  const MeetingsWithContractor({super.key});

  @override
  State<MeetingsWithContractor> createState() => _MeetingsWithContractorState();
}

class _MeetingsWithContractorState extends State<MeetingsWithContractor> {
  final ApiService _apiService = ApiService();
  bool _isSubmitting = false;

  String? _processItem = 'Select';
  final List<String> _processdropdownItems = ['Select', 'Add', 'Update'];

  String? _areaCodeUi; // For UI display "CODE - DESC"
  AreaCodeDto? _selectedAreaCodeDto; // To store the selected object

  final List<String> _purchaserdropdownItems = [
    'Select', 'Purchaser(Non Trade)', 'AUTHORISED DEALER', /* Add more as per your system */
  ];
  String? _purchaserItem = 'Select';


  final TextEditingController _submissionDateController = TextEditingController();
  final TextEditingController _reportDateController = TextEditingController();
  DateTime? _selectedSubmissionDate;
  DateTime? _selectedReportDate;

  final List<int> _uploadRows = [0];
  final ImagePicker _picker = ImagePicker();
  final List<File?> _selectedImages = [null];

  // Your other controllers (ensure names match your UI and DTO needs)
  final TextEditingController _codeController = TextEditingController(); // For "Code" field
  final TextEditingController _newOrderController = TextEditingController();
  final TextEditingController _ugaiRecoveryController = TextEditingController();
  final TextEditingController _grievanceController = TextEditingController();
  final TextEditingController _anyOtherPointController = TextEditingController();
  final TextEditingController _contractorNameController = TextEditingController(); // For Contrnam
  final TextEditingController _remarksController = TextEditingController();      // For Remarksc
  final TextEditingController _topicDiscussedController = TextEditingController(); // For Topcdiss

  // Location controllers (not explicitly in UI but needed for DTO)
  final TextEditingController _custLatitudeController = TextEditingController();
  final TextEditingController _custLongitudeController = TextEditingController();


  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // _apiService.currentLoginIdM = "your_actual_login_id"; // Set actual login ID
    _selectedSubmissionDate = DateTime.now();
    _submissionDateController.text = DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
    _selectedReportDate = DateTime.now();
    _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
  }


  @override
  void dispose() {
    _submissionDateController.dispose();
    _reportDateController.dispose();
    _codeController.dispose();
    _newOrderController.dispose();
    _ugaiRecoveryController.dispose();
    _grievanceController.dispose();
    _anyOtherPointController.dispose();
    _contractorNameController.dispose();
    _remarksController.dispose();
    _topicDiscussedController.dispose();
    _custLatitudeController.dispose();
    _custLongitudeController.dispose();
    super.dispose();
  }

  Future<void> _pickDate(bool isSubmissionDate) async {
    final now = DateTime.now();
    DateTime? initialDate = isSubmissionDate ? (_selectedSubmissionDate ?? now) : (_selectedReportDate ?? now);

    final picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      setState(() {
        if(isSubmissionDate) {
          _selectedSubmissionDate = picked;
          _submissionDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        } else {
          _selectedReportDate = picked;
          _reportDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        }
      });
    }
  }

  void _addRow() {
    if (_uploadRows.length >= 3) return;
    setState(() {
      _uploadRows.add(_uploadRows.length);
      _selectedImages.add(null);
    });
  }

  void _removeRow(int index) {
    if (_uploadRows.length <= 1) return;
    setState(() {
      if (index < _uploadRows.length && index < _selectedImages.length) {
        _uploadRows.removeAt(index);
        _selectedImages.removeAt(index);
        for(int i=0; i < _uploadRows.length; i++) {
          _uploadRows[i] = i;
        }
      }
      if (_uploadRows.isEmpty) {
        _uploadRows.add(0);
        _selectedImages.add(null);
      }
    });
  }

  Future<void> _pickImage(int index) async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImages[index] = File(pickedFile.path);
      });
    }
  }

  void _showImageDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
          child: InteractiveViewer(
            panEnabled: false,
            boundaryMargin: const EdgeInsets.all(20),
            minScale: 0.5,
            maxScale: 2,
            child: Image.file(imageFile, fit: BoxFit.contain),
          )
      ),
    );
  }

  Future<String> _fileToBase64(File? file) async {
    if (file == null) return '';
    final bytes = await file.readAsBytes();
    return base64Encode(bytes);
  }

  Future<void> _submitForm({bool exitAfter = false}) async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isSubmitting = true);

    final imgfirst  = await _fileToBase64(_selectedImages.isNotEmpty ? _selectedImages[0] : null);
    final imgscndd  = await _fileToBase64(_selectedImages.length > 1 ? _selectedImages[1] : null);
    final imgthird  = await _fileToBase64(_selectedImages.length > 2 ? _selectedImages[2] : null);

    final dto = MeetingContractorDto(
      proctype: _processItem == 'Select' ? 'Add' : _processItem!,
      submdate: _submissionDateController.text,
      repodate: _reportDateController.text,
      actdetl1: _contractorNameController.text,
      actdetl2: _topicDiscussedController.text,
      actdetl3: _remarksController.text,
      othrnote: _remarksController.text,
      imgfirst: imgfirst,
      imgscndd: imgscndd,
      imgthird: imgthird,
    );

    debugPrint("API PAYLOAD: ${jsonEncode(dto.toJson())}");

    try {
      // IMPORTANT: Replace "/specificactivity/meetingcontractor" with your actual endpoint
      // OR refactor this DTO and logic to use the main DSR submission.
      await _apiService.submitMeetingContractor(dto, "/dsractivity/meetingcontractor"); // Placeholder

      if(mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Meeting submitted successfully!'), backgroundColor: Colors.green),
        );
        if (exitAfter) {
          Navigator.of(context).pop();
        } else {
          _resetForm();
        }
      }
    } catch (e) {
      if(mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Submission failed: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if(mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    setState(() {
      _processItem = 'Select';
      _selectedSubmissionDate = DateTime.now();
      _submissionDateController.text = DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
      _selectedReportDate = DateTime.now();
      _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
      // _areaCodeUi = 'Select'; // Unused field
      _selectedAreaCodeDto = null;
      _purchaserItem = 'Select';
      _codeController.clear();
      _newOrderController.clear();
      _ugaiRecoveryController.clear();
      _grievanceController.clear();
      _anyOtherPointController.clear();
      _contractorNameController.clear();
      _remarksController.clear();
      _topicDiscussedController.clear();
      _custLatitudeController.clear();
      _custLongitudeController.clear();

      _uploadRows.clear();
      _selectedImages.clear();
      _uploadRows.add(0);
      _selectedImages.add(null);
    });
  }

  // Fetch Area Codes for DropdownSearch
  Future<List<AreaCodeDto>> _fetchAreaCodesForDropdown(String? filter) async {
    try {
      return await _apiService.getAreaCodes(search: filter);
    } catch (e) {
      // Handle error, maybe show a snackbar
      debugPrint("Error fetching area codes: $e");
      return [];
    }
  }


  Widget _buildSearchableDropdownField({
    required AreaCodeDto? selected, // Changed to AreaCodeDto
    required Future<List<AreaCodeDto>> Function(String?) onFind, // Changed signature
    required ValueChanged<AreaCodeDto?> onChanged, // Changed signature
    String? Function(AreaCodeDto?)? validator, // Changed signature
    required String hintText,
  }) => DropdownSearch<AreaCodeDto>(
    asyncItems: onFind,
    selectedItem: selected,
    onChanged: onChanged,
    validator: validator,
    popupProps: PopupProps.menu(
      showSearchBox: true,
      searchFieldProps: TextFieldProps(decoration: AppTheme.textFieldInputDecoration(hintText: 'Search...')),
      itemBuilder: (context, item, isSelected) => ListTile(title: Text(item.toString())),
      emptyBuilder: (context, searchEntry) => const Center(child: Padding(
        padding: EdgeInsets.all(12.0),
        child: Text('No areas found or type to search.'),
      )),
    ),
    dropdownDecoratorProps: DropDownDecoratorProps(
      dropdownSearchDecoration: AppTheme.dropdownDecoration(hintText: hintText),
    ),
    compareFn: (item1, item2) => item1.areaCode == item2.areaCode, // Important for object comparison
    itemAsString: (AreaCodeDto? u) => u?.toString() ?? '', // How to display selected item
  );


  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppTheme.scaffoldBackgroundColor,
        appBar: AppBar(
          leading: IconButton(
            onPressed: () {
              Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const DsrEntry()));
            },
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 22),
          ),
          title: Text('Meetings With Contractor', style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.white), overflow: TextOverflow.ellipsis),
          backgroundColor: AppTheme.primaryColor,
          elevation: 0,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.only(bottomLeft: Radius.circular(15), bottomRight: Radius.circular(15))),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter, end: Alignment.bottomCenter,
              colors: [AppTheme.scaffoldBackgroundColor, Colors.grey.shade100],
              stops: const [0.0, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  AppTheme.buildSectionCard(title: 'Process', icon: Icons.settings_outlined, children: [
                    AppTheme.buildLabel('Process Type'), const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _processItem,
                      decoration: AppTheme.dropdownDecoration(hintText: 'Select Process'),
                      items: _processdropdownItems.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
                      onChanged: (val) { if (val != null) setState(() => _processItem = val);},
                      validator: (value) => (value == null || value == 'Select') ? 'Required' : null,
                    ),
                  ]),
                  AppTheme.buildSectionCard(title: 'Date Information', icon: Icons.date_range_outlined, children: [
                    AppTheme.buildLabel('Submission Date'), const SizedBox(height: 8),
                    AppTheme.buildDateField(context, _submissionDateController, () => _pickDate(true), 'Select Submission Date', initialDate: _selectedSubmissionDate),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Report Date'), const SizedBox(height: 8),
                    AppTheme.buildDateField(context, _reportDateController, () => _pickDate(false), 'Select Report Date', initialDate: _selectedReportDate),
                  ]),
                  AppTheme.buildSectionCard(title: 'Location & Purchaser', icon: Icons.location_city_outlined, children: [
                    AppTheme.buildLabel('Area code *:'), const SizedBox(height: 8),
                    _buildSearchableDropdownField(
                      selected: _selectedAreaCodeDto,
                      onFind: _fetchAreaCodesForDropdown,
                      onChanged: (val) {
                        setState(() {
                          _selectedAreaCodeDto = val;
                          // If you had cityCoordinates map based on AreaCodeDto:
                          // if (val != null && _cityCoordinates.containsKey(val.areaCode)) {
                          //   _custLatitudeController.text = _cityCoordinates[val.areaCode]!['latitude']!.toString();
                          //   _custLongitudeController.text = _cityCoordinates[val.areaCode]!['longitude']!.toString();
                          // } else {
                          //   _custLatitudeController.clear();
                          //   _custLongitudeController.clear();
                          // }
                        });
                      },
                      validator: (value) => (value == null) ? 'Please select an Area Code' : null,
                      hintText: 'Select Area Code',
                    ),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Purchaser'), const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _purchaserItem,
                      decoration: AppTheme.dropdownDecoration(hintText: 'Select Purchaser Type'),
                      items: _purchaserdropdownItems.map((item) => DropdownMenuItem(value: item, child: Text(item))).toList(),
                      onChanged: (val) { if (val != null) setState(() => _purchaserItem = val);},
                      validator: (value) => (value == null || value == 'Select') ? 'Required' : null,
                    ),
                  ]),
                  AppTheme.buildSectionCard(title: 'Meeting & Order Details', icon: Icons.business_center_outlined, children: [
                    AppTheme.buildLabel('Code'), const SizedBox(height: 8), // Assuming this is a code for contractor/purchaser
                    AppTheme.buildTextField('Enter Code', controller: _codeController),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('New Order (MT)'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter New Order in MT', controller: _newOrderController, keyboardType: TextInputType.number),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('UGAI Recovery Plans'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter Recovery Plans', controller: _ugaiRecoveryController, maxLines: 2),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Any Grievance'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter Grievances', controller: _grievanceController, maxLines: 2),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Any Other Point'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter Other Points', controller: _anyOtherPointController, maxLines: 2),
                  ]),
                  AppTheme.buildSectionCard(title: 'Contractor & Discussion', icon: Icons.person_pin_circle_outlined, children: [
                    AppTheme.buildLabel('Contractor Name'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter Contractor Name', controller: _contractorNameController),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Topic Discussed'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter Topic Discussed', controller: _topicDiscussedController, maxLines: 3),
                    const SizedBox(height: 16),
                    AppTheme.buildLabel('Remarks'), const SizedBox(height: 8),
                    AppTheme.buildTextField('Enter Remarks', controller: _remarksController, maxLines: 3),
                  ]),

                  Container( /* ... Image Upload UI from AnyOtherActivity ... */
                      margin: const EdgeInsets.only(top: 20, bottom: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: AppTheme.cardDecoration,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(children: [ const Icon(Icons.photo_library_rounded, color: AppTheme.primaryColor, size: 24), const SizedBox(width: 8), Text('Supporting Documents', style: Theme.of(context).textTheme.titleLarge?.copyWith(color: AppTheme.primaryColor))]),
                          const SizedBox(height: 4),
                          Text('Upload images related to your meeting', style: Theme.of(context).textTheme.bodyMedium),
                          const SizedBox(height: 16),
                          ...List.generate(_uploadRows.length, (index) {
                            return Container(
                                margin: const EdgeInsets.only(bottom: 16),
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: _selectedImages[index] != null ? Colors.green.shade200 : Colors.grey.shade200, width: 1.5),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                        decoration: BoxDecoration(color: AppTheme.primaryColor.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(20)),
                                        child: Text('Document ${index + 1}', style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 14)),
                                      ),
                                      const Spacer(),
                                      if (_selectedImages[index] != null)
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                          decoration: BoxDecoration(color: Colors.green.shade100, borderRadius: BorderRadius.circular(20)),
                                          child: const Row(mainAxisSize: MainAxisSize.min, children: [ Icon(Icons.check_circle, color: Colors.green, size: 16), SizedBox(width: 4), Text('Uploaded', style: TextStyle(color: Colors.green, fontWeight: FontWeight.w500, fontSize: 14))]),
                                        ),
                                    ]),
                                    const SizedBox(height:16),
                                    if (_selectedImages[index] != null)
                                      GestureDetector(
                                        onTap: () => _showImageDialog(_selectedImages[index]!),
                                        child: Container(
                                          height: 120, width: double.infinity, margin: const EdgeInsets.only(bottom:16),
                                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), image: DecorationImage(image: FileImage(_selectedImages[index]!), fit: BoxFit.cover)),
                                          child: Align(alignment: Alignment.topRight, child: Container(margin: const EdgeInsets.all(8), padding: const EdgeInsets.all(4), decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.6), shape: BoxShape.circle), child: const Icon(Icons.zoom_in, color: Colors.white, size: 20))),
                                        ),
                                      ),
                                    Row(children: [
                                      Expanded(child: ElevatedButton.icon(onPressed: () => _pickImage(index), icon: Icon(_selectedImages[index] != null ? Icons.refresh:Icons.upload_file, size:18), label: Text(_selectedImages[index] != null?'Replace':'Upload'), style: AppTheme.elevatedButtonStyle(bgColor: _selectedImages[index] != null ? Colors.amber.shade600 : AppTheme.primaryColor))),
                                      if(_selectedImages[index] != null)...[
                                        const SizedBox(width:8),
                                        Expanded(child: ElevatedButton.icon(onPressed:()=> _showImageDialog(_selectedImages[index]!), icon:const Icon(Icons.visibility, size:18), label:const Text('View'), style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor))),
                                      ]
                                    ],)
                                  ],
                                )
                            );
                          }),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (_uploadRows.length < 3) ElevatedButton.icon(onPressed: _addRow, icon: const Icon(Icons.add_photo_alternate, size: 20), label: const Text('Add Document'), style: AppTheme.elevatedButtonStyle()),
                              if (_uploadRows.length > 1) ...[ const SizedBox(width: 12), ElevatedButton.icon(onPressed: () => _removeRow(_uploadRows.length - 1), icon: const Icon(Icons.remove_circle_outline, size: 20), label: const Text('Remove Last'), style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.dangerButtonColor))]
                            ],
                          ),
                        ],
                      )
                  ),
                  const SizedBox(height: 30),
                  Container( // Submit buttons card
                      padding: const EdgeInsets.all(20),
                      decoration: AppTheme.cardDecoration,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(children: [const Icon(Icons.save_alt_rounded, color: AppTheme.primaryColor, size:24), const SizedBox(width:8), Text('Submit Meeting', style:Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppTheme.primaryColor))]),
                          const SizedBox(height:20),
                          ElevatedButton(
                            onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: false),
                            style: AppTheme.elevatedButtonStyle(),
                            child: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & New'),
                          ),
                          const SizedBox(height: 12),
                          ElevatedButton(
                            onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: true),
                            style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                            child: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & Exit'),
                          ),
                        ],
                      )
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}