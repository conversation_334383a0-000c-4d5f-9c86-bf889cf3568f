// lib/dsr_entry_screen/btl_activites.dart

import 'dart:io';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';

import '../theme/app_theme.dart';
import 'dsr_entry.dart';
import '../models.dart';        // Provides BtlActivityDto
import '../api_service.dart';   // Provides submitBtlActivity(...)

class BtlActivities extends StatefulWidget {
  const BtlActivities({Key? key}) : super(key: key);

  @override
  State<BtlActivities> createState() => _BtlActivitiesState();
}

class _BtlActivitiesState extends State<BtlActivities> {
  final ApiService _apiService = ApiService();
  bool _isSubmitting = false;

  final _formKey = GlobalKey<FormState>();

  /// “Process” dropdown state: Add / Update
  String? _processItem = 'Select';
  final List<String> _processdropdownItems = ['Select', 'Add', 'Update'];

  /// Date pickers (Submission & Report)
  final TextEditingController _submissionDateController = TextEditingController();
  final TextEditingController _reportDateController = TextEditingController();
  DateTime? _selectedSubmissionDate;
  DateTime? _selectedReportDate;

  /// Main BTL-specific fields:
  final TextEditingController _btlTypeController = TextEditingController();      // e.g. “Sampling,” “Demonstration,” etc.
  final TextEditingController _locationController = TextEditingController();     // Where the BTL activity was done
  final TextEditingController _quantityController = TextEditingController();     // Quantity involved (if applicable)
  final TextEditingController _remarksController = TextEditingController();      // Any remarks or other notes

  /// Image‐upload: up to 3 “Documents”
  final List<int> _uploadRows = [0];
  final List<File?> _selectedImages = [null];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    // Initialize both dates to “today”
    _selectedSubmissionDate = DateTime.now();
    _submissionDateController.text =
        DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);

    _selectedReportDate = DateTime.now();
    _reportDateController.text =
        DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
    // Note: in a real scenario, you might set _apiService.currentLoginIdM here.
  }

  @override
  void dispose() {
    _submissionDateController.dispose();
    _reportDateController.dispose();
    _btlTypeController.dispose();
    _locationController.dispose();
    _quantityController.dispose();
    _remarksController.dispose();
    super.dispose();
  }

  /// Show a DatePicker and update the corresponding controller
  Future<void> _pickDate(bool isSubmissionDate) async {
    final now = DateTime.now();
    DateTime initial =
    isSubmissionDate ? (_selectedSubmissionDate ?? now) : (_selectedReportDate ?? now);

    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initial,
      firstDate: DateTime(2000),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );

    if (picked != null) {
      setState(() {
        if (isSubmissionDate) {
          _selectedSubmissionDate = picked;
          _submissionDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        } else {
          _selectedReportDate = picked;
          _reportDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        }
      });
    }
  }

  /// Add another “Document” row (max 3)
  void _addRow() {
    if (_uploadRows.length >= 3) return;
    setState(() {
      _uploadRows.add(_uploadRows.length);
      _selectedImages.add(null);
    });
  }

  /// Remove the last “Document” row (leaving at least one)
  void _removeRow(int index) {
    if (_uploadRows.length <= 1) return;
    setState(() {
      if (index < _uploadRows.length && index < _selectedImages.length) {
        _uploadRows.removeAt(index);
        _selectedImages.removeAt(index);
        // Re‐index the remaining rows:
        for (int i = 0; i < _uploadRows.length; i++) {
          _uploadRows[i] = i;
        }
      }
      if (_uploadRows.isEmpty) {
        _uploadRows.add(0);
        _selectedImages.add(null);
      }
    });
  }

  /// Pick a single image from gallery for given index
  Future<void> _pickImage(int index) async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImages[index] = File(pickedFile.path);
      });
    }
  }

  /// Show an enlarged preview of a selected image
  void _showImageDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: InteractiveViewer(
          panEnabled: false,
          boundaryMargin: const EdgeInsets.all(20),
          minScale: 0.5,
          maxScale: 2,
          child: Image.file(imageFile, fit: BoxFit.contain),
        ),
      ),
    );
  }

  /// Base64‐encode a File? or return empty string if null
  Future<String> _fileToBase64(File? file) async {
    if (file == null) return '';
    final bytes = await file.readAsBytes();
    return base64Encode(bytes);
  }

  /// Submit the BTL Activity
  Future<void> _submitForm({bool exitAfter = false}) async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSubmitting = true);

    // Convert up to three images to Base64:
    final imgfirst  = await _fileToBase64(_selectedImages.isNotEmpty ? _selectedImages[0] : null);
    final imgscndd  = await _fileToBase64(_selectedImages.length > 1 ? _selectedImages[1] : null);
    final imgthird  = await _fileToBase64(_selectedImages.length > 2 ? _selectedImages[2] : null);

    // Build the DTO
    final dto = BtlActivityDto(
      proctype: _processItem == 'Select' ? 'Add' : _processItem!,
      submdate: _submissionDateController.text,
      repodate: _reportDateController.text,
      actdetl1: _btlTypeController.text.trim(),        // BTL Type
      actdetl2: _locationController.text.trim(),       // Location
      actdetl3: _quantityController.text.trim(),       // Quantity (string)
      othrnote: _remarksController.text.trim(),        // Any additional remarks
      imgfirst: imgfirst.isNotEmpty ? imgfirst : null,
      imgscndd: imgscndd.isNotEmpty ? imgscndd : null,
      imgthird: imgthird.isNotEmpty ? imgthird : null,
    );

    try {
      await _apiService.submitBtlActivity(dto, "/dsractivity/btlactivity");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('BTL Activity submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        if (exitAfter) {
          Navigator.of(context).pop();
        } else {
          _resetForm();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Submission failed: $e'),
            backgroundColor: Colors.redAccent,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  /// Reset all fields to initial state
  void _resetForm() {
    _formKey.currentState?.reset();
    setState(() {
      _processItem = 'Select';
      _selectedSubmissionDate = DateTime.now();
      _submissionDateController.text =
          DateFormat('yyyy-MM-dd').format(_selectedSubmissionDate!);
      _selectedReportDate = DateTime.now();
      _reportDateController.text =
          DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
      _btlTypeController.clear();
      _locationController.clear();
      _quantityController.clear();
      _remarksController.clear();
      _uploadRows.clear();
      _selectedImages.clear();
      _uploadRows.add(0);
      _selectedImages.add(null);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (_) => const DsrEntry()),
            );
          },
        ),
        title: Text(
          'BTL Activities',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // ── Process Section ───────────────────────────────────────────────────
              AppTheme.buildSectionCard(
                title: 'Process',
                icon: Icons.settings_outlined,
                children: [
                  AppTheme.buildLabel('Process Type'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _processItem,
                    decoration: AppTheme.dropdownDecoration(hintText: 'Select Process'),
                    items: _processdropdownItems
                        .map((item) => DropdownMenuItem(value: item, child: Text(item)))
                        .toList(),
                    onChanged: (val) {
                      if (val != null) setState(() => _processItem = val);
                    },
                    validator: (value) =>
                    (value == null || value == 'Select') ? 'Please select a process' : null,
                    isExpanded: true,
                  ),
                ],
              ),

              // ── Date Information Section ────────────────────────────────────────
              AppTheme.buildSectionCard(
                title: 'Date Information',
                icon: Icons.date_range_outlined,
                children: [
                  AppTheme.buildLabel('Submission Date'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(
                    context,
                    _submissionDateController,
                        () => _pickDate(true),
                    'Select Submission Date',
                    initialDate: _selectedSubmissionDate,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Report Date'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(
                    context,
                    _reportDateController,
                        () => _pickDate(false),
                    'Select Report Date',
                    initialDate: _selectedReportDate,
                  ),
                ],
              ),

              // ── BTL Details Section ─────────────────────────────────────────────
              AppTheme.buildSectionCard(
                title: 'BTL Details',
                icon: Icons.business_center_outlined,
                children: [
                  AppTheme.buildLabel('BTL Type'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField(
                    'Enter BTL type (e.g., Sampling)',
                    controller: _btlTypeController,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Location'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField(
                    'Enter location of activity',
                    controller: _locationController,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Quantity (if applicable)'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField(
                    'Enter quantity',
                    controller: _quantityController,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Quantity is required';
                      }
                      if (double.tryParse(value.trim()) == null) {
                        return 'Enter a valid number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Remarks (Optional)'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField(
                    'Any remarks',
                    controller: _remarksController,
                    maxLines: 3,
                    validator: (val) => null, // optional
                  ),
                ],
              ),

              // ── Image Upload Section ─────────────────────────────────────────────
              Container(
                margin: const EdgeInsets.only(top: 20, bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: AppTheme.cardDecoration,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.photo_library_rounded, color: AppTheme.primaryColor, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          'Supporting Documents',
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(color: AppTheme.primaryColor),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Upload up to 3 images (if any).',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),

                    // Dynamically generate each “Document n” row
                    ...List.generate(_uploadRows.length, (index) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _selectedImages[index] != null
                                ? Colors.green.shade200
                                : Colors.grey.shade200,
                            width: 1.5,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    'Document ${index + 1}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.primaryColor,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                if (_selectedImages[index] != null)
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade100,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: const Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(Icons.check_circle, color: Colors.green, size: 16),
                                        SizedBox(width: 4),
                                        Text(
                                          'Uploaded',
                                          style: TextStyle(
                                            color: Colors.green,
                                            fontWeight: FontWeight.w500,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            if (_selectedImages[index] != null)
                              GestureDetector(
                                onTap: () => _showImageDialog(_selectedImages[index]!),
                                child: Container(
                                  height: 120,
                                  width: double.infinity,
                                  margin: const EdgeInsets.only(bottom: 16),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    image: DecorationImage(
                                      image: FileImage(_selectedImages[index]!),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                  child: Align(
                                    alignment: Alignment.topRight,
                                    child: Container(
                                      margin: const EdgeInsets.all(8),
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.6),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(Icons.zoom_in, color: Colors.white, size: 20),
                                    ),
                                  ),
                                ),
                              ),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _pickImage(index),
                                    icon: Icon(
                                      _selectedImages[index] != null
                                          ? Icons.refresh
                                          : Icons.upload_file,
                                      size: 18,
                                    ),
                                    label: Text(
                                      _selectedImages[index] != null ? 'Replace' : 'Upload',
                                    ),
                                    style: AppTheme.elevatedButtonStyle(
                                      bgColor: _selectedImages[index] != null
                                          ? Colors.amber.shade600
                                          : AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                                if (_selectedImages[index] != null) ...[
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () => _showImageDialog(_selectedImages[index]!),
                                      icon: const Icon(Icons.visibility, size: 18),
                                      label: const Text('View'),
                                      style: AppTheme.elevatedButtonStyle(
                                          bgColor: AppTheme.successColor),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      );
                    }),

                    // “Add Document” / “Remove Last” buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_uploadRows.length < 3)
                          ElevatedButton.icon(
                            onPressed: _addRow,
                            icon: const Icon(Icons.add_photo_alternate, size: 20),
                            label: const Text('Add Document'),
                            style: AppTheme.elevatedButtonStyle(),
                          ),
                        if (_uploadRows.length > 1) ...[
                          const SizedBox(width: 12),
                          ElevatedButton.icon(
                            onPressed: () => _removeRow(_uploadRows.length - 1),
                            icon: const Icon(Icons.remove_circle_outline, size: 20),
                            label: const Text('Remove Last'),
                            style: AppTheme.elevatedButtonStyle(
                                bgColor: AppTheme.dangerButtonColor),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // ── Submit Buttons ───────────────────────────────────────────────────
              Container(
                padding: const EdgeInsets.all(20),
                decoration: AppTheme.cardDecoration,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.save_alt_rounded, color: AppTheme.primaryColor, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          'Submit BTL Activity',
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(color: AppTheme.primaryColor),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: false),
                      icon: const Icon(Icons.save_outlined),
                      label: _isSubmitting
                          ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
                      )
                          : const Text('Submit & New'),
                      style: AppTheme.elevatedButtonStyle(),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: true),
                      icon: const Icon(Icons.check_circle_outline),
                      label: _isSubmitting
                          ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
                      )
                          : const Text('Submit & Exit'),
                      style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                    ),
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(builder: (_) => const DsrEntry()),
                        );
                      },
                      icon: const Icon(Icons.arrow_back, color: AppTheme.primaryColor),
                      label: const Text('Back to DSR Entry'),
                      style: AppTheme.outlinedButtonStyle(),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
