import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
// For image picking, if you add image upload functionality later
// import 'dart:io';
// import 'package:image_picker/image_picker.dart';
// import 'dart:convert';

import '../theme/app_theme.dart';
// import '../models.dart'; // Assuming you'll create a KycDataDto here - unused for now
import '../api_service.dart'; // Assuming you'll add KYC methods here
// import 'dsr_entry.dart'; // If you need to navigate back to DSR entry

class EditKyc extends StatefulWidget {
  // If this screen is for an existing KYC, you'd pass the purchaserCode/ID
  // final String? purchaserCode;
  // const EditKyc({Key? key, this.purchaserCode}) : super(key: key);

  const EditKyc({Key? key}) : super(key: key);

  @override
  State<EditKyc> createState() => _EditKycState();
}

class _EditKycState extends State<EditKyc> {
  final ApiService _apiService = ApiService(); // Will be used for submission
  bool _isSubmitting = false;
  final bool _isLoadingDetails = false; // If you implement fetching existing KYC

  // ── Controllers for “Area Code” & “Purchaser Search” ─────────────────────────────
  String? _selectedAreaCode; // Changed to nullable for 'Select' state
  final List<String> _areaCodeOptions = ['Select', 'Jodhpur', 'Jaipur', 'Udaipur']; // Example
  final TextEditingController _purchaserSearchController = TextEditingController();

  // ── Controllers for “Basic Information” ───────────────────────────────────────────
  final TextEditingController _purchaserCodeController = TextEditingController();
  final TextEditingController _purchaserNameController = TextEditingController();
  String? _selectedMarketingEmployee;
  final List<String> _marketingEmployeeOptions = ['Select', 'Employee A', 'Employee B']; // Example
  String? _selectedDistrict;
  final List<String> _districtOptions = ['Select', 'District X', 'District Y']; // Example

  // TODO: Add controllers for image paths/files if you implement image upload

  // ── Controllers for “Personal Information” ────────────────────────────────────────
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _middleNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _anniversaryController = TextEditingController();
  final TextEditingController _wifeNameController = TextEditingController();
  final TextEditingController _firstChildController = TextEditingController();
  final TextEditingController _secondChildController = TextEditingController();
  final TextEditingController _thirdChildController = TextEditingController();
  final TextEditingController _fourthChildController = TextEditingController();

  // ── Controllers for “Communication Details” ────────────────────────────────────────
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _officePhone1Controller = TextEditingController();
  final TextEditingController _officePhone2Controller = TextEditingController();
  final TextEditingController _resiPhone1Controller = TextEditingController();
  final TextEditingController _resiPhone2Controller = TextEditingController();
  final TextEditingController _faxController = TextEditingController();

  // ── Controllers for “Legal Information” ───────────────────────────────────────────
  String? _selectedGstTaxClassification;
  final List<String> _gstClassificationOptions = ['Select', 'Manufacturer', 'Trader', 'Service']; // Example
  final TextEditingController _provisionalGstIdController = TextEditingController();
  final TextEditingController _panController = TextEditingController();
  final TextEditingController _tanController = TextEditingController();
  final TextEditingController _arnController = TextEditingController();
  final TextEditingController _lstTinController = TextEditingController();
  final TextEditingController _cstController = TextEditingController();

  // ── Controllers for “Others” ───────────────────────────────────────────────────────
  String? _selectedSparshLogin;
  String? _selected194qAllow;
  String? _selectedAgreementStatus;
  String? _selectedUbsStkFlag;
  final List<String> _yesNoOptions = ['Select', 'Yes', 'No']; // For Yes/No dropdowns
  final List<String> _agreementStatusOptions = ['Select', 'Signed', 'Pending', 'NA']; // Example

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // _apiService.currentLoginIdM = "your_actual_login_id"; // Set from auth
    // If widget.purchaserCode is provided, you would fetch KYC details here
    // if (widget.purchaserCode != null) {
    //   _fetchKycDetails(widget.purchaserCode!);
    // }
  }

  // Future<void> _fetchKycDetails(String purchaserCode) async {
  //   setState(() => _isLoadingDetails = true);
  //   try {
  //     // final kycData = await _apiService.getKycDetails(purchaserCode);
  //     // _populateForm(kycData); // You'd need a _populateForm method
  //   } catch (e) {
  //     // Handle error
  //   } finally {
  //     if (mounted) setState(() => _isLoadingDetails = false);
  //   }
  // }

  // void _populateForm(KycDataDto data) { /* ... set all controllers ... */ }


  @override
  void dispose() {
    // Dispose all controllers
    _purchaserSearchController.dispose(); _purchaserCodeController.dispose(); _purchaserNameController.dispose();
    _firstNameController.dispose(); _middleNameController.dispose(); _lastNameController.dispose();
    _dobController.dispose(); _anniversaryController.dispose(); _wifeNameController.dispose();
    _firstChildController.dispose(); _secondChildController.dispose(); _thirdChildController.dispose(); _fourthChildController.dispose();
    _mobileController.dispose(); _emailController.dispose(); _officePhone1Controller.dispose(); _officePhone2Controller.dispose();
    _resiPhone1Controller.dispose(); _resiPhone2Controller.dispose(); _faxController.dispose();
    _provisionalGstIdController.dispose(); _panController.dispose(); _tanController.dispose();
    _arnController.dispose(); _lstTinController.dispose(); _cstController.dispose();
    super.dispose();
  }

  Future<void> _pickDate(TextEditingController controller) async {
    DateTime initialDate = DateTime.now();
    if (controller.text.isNotEmpty) {
      try {
        // Assuming dates are stored as yyyy-MM-dd for API, but displayed as dd/MM/yyyy
        initialDate = DateFormat('yyyy-MM-dd').parse(controller.text);
      } catch (_) {
        try { initialDate = DateFormat('dd/MM/yyyy').parse(controller.text); } catch(_){}
      }
    }
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      // Store in API format (yyyy-MM-dd), display can be different if needed via DateFormat
      controller.text = DateFormat('yyyy-MM-dd').format(picked);
    }
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    bool readOnly = false,
    VoidCallback? onTap,
    Widget? suffixIcon,
    TextInputType? keyboardType, // Added keyboardType
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
          controller: controller,
          readOnly: readOnly,
          onTap: onTap,
          keyboardType: keyboardType ?? TextInputType.text, // Use provided or default
          decoration: AppTheme.textFieldInputDecoration(hintText: labelText, labelText: labelText).copyWith(
            suffixIcon: suffixIcon,
          ),
          validator: validator ?? (val) {
            if (!labelText.endsWith('(Optional)') && (val == null || val.trim().isEmpty)) {
              return '$labelText is required';
            }
            return null;
          }
      ),
    );
  }

  Widget _buildDropdownField({
    required String? currentValue,
    required List<String> items,
    required String labelText,
    required ValueChanged<String?> onChanged,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<String>(
        value: currentValue,
        decoration: AppTheme.dropdownDecoration(hintText: labelText),
        items: items.map((String value) => DropdownMenuItem<String>(value: value, child: Text(value))).toList(),
        onChanged: onChanged,
        validator: validator ?? (val) => (val == null || val == 'Select') ? '$labelText is required' : null,
        isExpanded: true,
      ),
    );
  }

  Future<void> _submitKycData() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please correct errors in the form.'), backgroundColor: Colors.red),
      );
      return;
    }
    setState(() => _isSubmitting = true);

    // TODO: 1. Create a KycDataDto in models.dart with all these fields.
    // final kycDto = KycDataDto(
    //   areaCode: _selectedAreaCode,
    //   purchaserCode: _purchaserCodeController.text,
    //   purchaserName: _purchaserNameController.text,
    //   // ... and so on for ALL fields
    //   marketingEmployee: _selectedMarketingEmployee,
    //   district: _selectedDistrict,
    //   firstName: _firstNameController.text,
    //   // ... map all controllers and selected dropdown values
    // );

    try {
      // TODO: 2. Add a method like `submitKycUpdate(KycDataDto dto)` to your ApiService.
      // TODO: 3. Create the corresponding POST/PUT endpoint in your .NET API.
      // await _apiService.submitKycUpdate(kycDto);

      // --- Placeholder Success ---
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('KYC data (simulated) submitted!'), backgroundColor: Colors.green),
        );
        Navigator.of(context).pop(); // Go back after successful submission
      }
      // --- End Placeholder ---

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('KYC submission failed: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit KYC'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoadingDetails
          ? const Center(child: CircularProgressIndicator())
          : Container(
        decoration: AppTheme.gradientBackground, // Assuming you have this in AppTheme
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppTheme.buildSectionCard(title: 'Area & Purchaser', icon: Icons.map_outlined, children: [
                    _buildDropdownField(labelText: 'Area Code *', currentValue: _selectedAreaCode, items: _areaCodeOptions, onChanged: (val) => setState(() => _selectedAreaCode = val)),
                    _buildTextFormField(controller: _purchaserSearchController, labelText: 'Purchaser (Name or Code) *', suffixIcon: IconButton(icon: const Icon(Icons.search), onPressed: () { /* TODO: Implement purchaser search to populate fields */ })),
                  ]),

                  AppTheme.buildSectionCard(title: 'Basic Information', icon: Icons.person_outline, children: [
                    _buildTextFormField(controller: _purchaserCodeController, labelText: 'Purchaser Code *', readOnly: true), // Often read-only after search
                    _buildTextFormField(controller: _purchaserNameController, labelText: 'Purchaser Name *', readOnly: true), // Often read-only after search
                    // TODO: Add Image Upload/View buttons here if needed (Profile Image, GST Supporting)
                    _buildDropdownField(labelText: 'Concerned Marketing Employee *', currentValue: _selectedMarketingEmployee, items: _marketingEmployeeOptions, onChanged: (val) => setState(() => _selectedMarketingEmployee = val)),
                    _buildDropdownField(labelText: 'District (State – Area) *', currentValue: _selectedDistrict, items: _districtOptions, onChanged: (val) => setState(() => _selectedDistrict = val)),
                  ]),

                  AppTheme.buildSectionCard(title: 'Personal Information', icon: Icons.family_restroom_outlined, children: [
                    _buildTextFormField(controller: _firstNameController, labelText: 'First Name *'),
                    _buildTextFormField(controller: _middleNameController, labelText: 'Middle Name (Optional)'),
                    _buildTextFormField(controller: _lastNameController, labelText: 'Last Name *'),
                    _buildTextFormField(controller: _dobController, labelText: 'Date of Birth * (yyyy-MM-dd)', readOnly: true, onTap: () => _pickDate(_dobController), suffixIcon: const Icon(Icons.calendar_today, color: AppTheme.primaryColor)),
                    _buildTextFormField(controller: _anniversaryController, labelText: 'Anniversary Date (Optional, yyyy-MM-dd)', readOnly: true, onTap: () => _pickDate(_anniversaryController), suffixIcon: const Icon(Icons.calendar_today, color: AppTheme.primaryColor)),
                    _buildTextFormField(controller: _wifeNameController, labelText: 'Wife Name (Optional)'),
                    _buildTextFormField(controller: _firstChildController, labelText: 'First Child (Optional)'),
                    _buildTextFormField(controller: _secondChildController, labelText: 'Second Child (Optional)'),
                    _buildTextFormField(controller: _thirdChildController, labelText: 'Third Child (Optional)'),
                    _buildTextFormField(controller: _fourthChildController, labelText: 'Fourth Child (Optional)'),
                  ]),

                  AppTheme.buildSectionCard(title: 'Communication Details', icon: Icons.contact_phone_outlined, children: [
                    _buildTextFormField(controller: _mobileController, labelText: 'Mobile *', keyboardType: TextInputType.phone),
                    _buildTextFormField(controller: _emailController, labelText: 'Email *', keyboardType: TextInputType.emailAddress, validator: (val) {
                      if (val == null || val.trim().isEmpty) return 'Email is required';
                      if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(val)) return 'Enter a valid email';
                      return null;
                    }),
                    _buildTextFormField(controller: _officePhone1Controller, labelText: 'Office Phone 1 (Optional)', keyboardType: TextInputType.phone),
                    _buildTextFormField(controller: _officePhone2Controller, labelText: 'Office Phone 2 (Optional)', keyboardType: TextInputType.phone),
                    _buildTextFormField(controller: _resiPhone1Controller, labelText: 'Residence Phone 1 (Optional)', keyboardType: TextInputType.phone),
                    _buildTextFormField(controller: _resiPhone2Controller, labelText: 'Residence Phone 2 (Optional)', keyboardType: TextInputType.phone),
                    _buildTextFormField(controller: _faxController, labelText: 'Fax (Optional)', keyboardType: TextInputType.phone),
                  ]),

                  AppTheme.buildSectionCard(title: 'Legal Information', icon: Icons.gavel_outlined, children: [
                    _buildDropdownField(labelText: 'GST Tax Classification *', currentValue: _selectedGstTaxClassification, items: _gstClassificationOptions, onChanged: (val) => setState(() => _selectedGstTaxClassification = val)),
                    _buildTextFormField(controller: _provisionalGstIdController, labelText: 'Provisional GST ID / GSTIN *'),
                    _buildTextFormField(controller: _panController, labelText: 'PAN No *', validator: (val) {
                      if (val == null || val.trim().isEmpty) return 'PAN is required';
                      if (!RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$').hasMatch(val.toUpperCase())) return 'Invalid PAN format';
                      return null;
                    }),
                    _buildTextFormField(controller: _tanController, labelText: 'TAN No (Optional)'),
                    // If "Update Tan Number" is a separate action, consider its flow. For now, just a field.
                    _buildTextFormField(controller: _arnController, labelText: 'ARN No (Optional)'),
                    _buildTextFormField(controller: _lstTinController, labelText: 'LST TIN No (Optional)'),
                    _buildTextFormField(controller: _cstController, labelText: 'CST No (Optional)'),
                  ]),
                  AppTheme.buildSectionCard(title: 'Other Details', icon: Icons.more_horiz_outlined, children: [
                    _buildDropdownField(labelText: 'SPARSH Login *', currentValue: _selectedSparshLogin, items: _yesNoOptions, onChanged: (val) => setState(() => _selectedSparshLogin = val)),
                    _buildDropdownField(labelText: '194Q Communication Document Allow *', currentValue: _selected194qAllow, items: _yesNoOptions, onChanged: (val) => setState(() => _selected194qAllow = val)),
                    _buildDropdownField(labelText: 'Agreement Status *', currentValue: _selectedAgreementStatus, items: _agreementStatusOptions, onChanged: (val) => setState(() => _selectedAgreementStatus = val)),
                    _buildDropdownField(labelText: 'UBS and STK Flag *', currentValue: _selectedUbsStkFlag, items: _yesNoOptions, onChanged: (val) => setState(() => _selectedUbsStkFlag = val)),
                  ]),

                  const SizedBox(height: 24),
                  Center(
                    child: ElevatedButton.icon(
                      icon: _isSubmitting ? const SizedBox.shrink() : const Icon(Icons.save_alt_outlined),
                      label: _isSubmitting
                          ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3))
                          : const Text('Submit KYC'),
                      onPressed: _isSubmitting ? null : _submitKycData,
                      style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}