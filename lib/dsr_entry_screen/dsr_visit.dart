// lib/dsr_entry_screen/DsrVisit.dart

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models.dart';
import '../api_service.dart';
import '../theme/app_theme.dart';

// Helper class for OrderRow
class OrderRow {
  final TextEditingController productController = TextEditingController(); // Maps to repoCatg in DTO
  final TextEditingController skuController = TextEditingController();     // Maps to prodCode in DTO
  final TextEditingController qtyBagsController = TextEditingController();
  final VoidCallback? onQtyChanged;

  OrderRow({this.onQtyChanged}) {
    if (onQtyChanged != null) {
      qtyBagsController.addListener(onQtyChanged!);
    }
  }

  void dispose() {
    if (onQtyChanged != null) qtyBagsController.removeListener(onQtyChanged!);
    productController.dispose();
    skuController.dispose();
    qtyBagsController.dispose();
  }

  DsrOrderItemDto toDto() {
    double? bags = double.tryParse(qtyBagsController.text.trim());
    double? mt = bags != null ? bags * DsrVisitState.bagToMtFactor : null;

    return DsrOrderItemDto(
      repoCatg: productController.text.trim().isNotEmpty ? productController.text.trim() : null,
      prodCode: skuController.text.trim().isNotEmpty ? skuController.text.trim() : null,
      quantityInBags: bags?.toInt(),
      quantityInMT: mt,
    );
  }
}

// Helper class for GiftRow
class GiftRow {
  String? giftType;
  final TextEditingController qtyController = TextEditingController();

  GiftRow({this.giftType});

  void dispose() {
    qtyController.dispose();
  }

  DsrGiftItemDto toDto() {
    return DsrGiftItemDto(
      giftTypeCode: giftType == 'Select' || giftType == null || giftType!.isEmpty ? null : giftType,
      quantity: int.tryParse(qtyController.text.trim()),
    );
  }
}

// Helper class for Competitor Sales Row (NEW)
class CompetitorSaleRow {
  final String brandName; // Fixed as per JSP
  final TextEditingController wcQtyController = TextEditingController();
  final TextEditingController wcpQtyController = TextEditingController();

  CompetitorSaleRow({required this.brandName});

  void dispose() {
    wcQtyController.dispose();
    wcpQtyController.dispose();
  }

  DsrCompetitorAvgSaleDto toDto() {
    return DsrCompetitorAvgSaleDto(
      competitorName: brandName,
      wcQty: wcQtyController.text.trim().isNotEmpty ? wcQtyController.text.trim() : null,
      wcpQty: wcpQtyController.text.trim().isNotEmpty ? wcpQtyController.text.trim() : null,
    );
  }
}

// Helper class for Market WCP SKU Row (NEW - if you implement its UI)
class MarketWcpSkuRow {
  final TextEditingController brandNameController = TextEditingController();
  final TextEditingController productCodeMarketController = TextEditingController(); // prdCodMk
  final TextEditingController bPriceController = TextEditingController(); // BPriceVl
  final TextEditingController cPriceController = TextEditingController(); // CPriceVl

  void dispose() {
    brandNameController.dispose();
    productCodeMarketController.dispose();
    bPriceController.dispose();
    cPriceController.dispose();
  }

  DsrMarketWcpSkuDto toDto() {
    return DsrMarketWcpSkuDto(
      brandName: brandNameController.text.trim().isNotEmpty ? brandNameController.text.trim() : null,
      prdCodMk: productCodeMarketController.text.trim().isNotEmpty ? productCodeMarketController.text.trim() : null,
      bPriceVl: bPriceController.text.trim().isNotEmpty ? bPriceController.text.trim() : null,
      cPriceVl: cPriceController.text.trim().isNotEmpty ? cPriceController.text.trim() : null,
    );
  }
}

class DsrVisit extends StatefulWidget {
  final String docuNumb;
  final String areaCode;
  final String retailerCode; // cusRtlCd
  final String retailerName; // cusRtlNm
  final String loginId;
  final String? initialGeoLatit;
  final String? initialGeoLongt;
  final String? initialPurchaserLatit;
  final String? initialPurchaserLongt;

  const DsrVisit({
    Key? key,
    required this.docuNumb,
    required this.areaCode,
    required this.retailerCode,
    required this.retailerName,
    required this.loginId,
    this.initialGeoLatit,
    this.initialGeoLongt,
    this.initialPurchaserLatit,
    this.initialPurchaserLongt,
  }) : super(key: key);

  @override
  DsrVisitState createState() => DsrVisitState();
}

class DsrVisitState extends State<DsrVisit> {
  final ApiService _apiService = ApiService();
  bool _isLoading = false;
  bool _isFetchingDetails = false;

  final TextEditingController documentNoController = TextEditingController();
  final TextEditingController purchaserTypeController = TextEditingController(); // cusRtlFl from header
  final TextEditingController areaCodeController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController nameController = TextEditingController();

  final TextEditingController kycStatusController = TextEditingController();
  final TextEditingController reportDateController = TextEditingController();
  final TextEditingController marketNameController = TextEditingController();
  final TextEditingController pendingIssueSpecifyTextController = TextEditingController();

  final TextEditingController enrolWCController = TextEditingController();
  final TextEditingController enrolWCPController = TextEditingController();
  final TextEditingController enrolVAPController = TextEditingController();
  final TextEditingController bwStockWCController = TextEditingController();
  final TextEditingController bwStockWCPController = TextEditingController();
  final TextEditingController bwStockVAPController = TextEditingController();

  final TextEditingController last3MonthsBwWCController = TextEditingController();
  final TextEditingController last3MonthsBwWCPController = TextEditingController();
  final TextEditingController last3MonthsBwVAPController = TextEditingController();
  final TextEditingController currentMonthBwWCController = TextEditingController();
  final TextEditingController currentMonthBwWCPController = TextEditingController();
  final TextEditingController currentMonthBwVAPController = TextEditingController();

  // For Competitor Sales (Last 3 Months Avg)
  final List<CompetitorSaleRow> competitorSaleRows = [
    CompetitorSaleRow(brandName: 'JK'),
    CompetitorSaleRow(brandName: 'Asian'),
    CompetitorSaleRow(brandName: 'Other'),
  ];

  // For Market WCP Highest Selling SKU
  final List<MarketWcpSkuRow> marketWcpSkuRows = [];

  final TextEditingController wcIndustryVolumeController = TextEditingController();
  final TextEditingController wcpIndustryVolumeController = TextEditingController();
  List<String> _selectedBrandsWC = [];
  final List<String> _allBrandsWC = ['BW', 'JK', 'RAK', 'OT'];
  List<String> _selectedBrandsWCP = [];
  final List<String> _allBrandsWCP = ['BW', 'JK', 'AP', 'BG', 'AC', 'PM', 'OT'];

  final List<OrderRow> orderRows = [];
  final TextEditingController orderExecDateController = TextEditingController();
  final TextEditingController otherRemarksController = TextEditingController();

  String? selectedTileSeller; // 'Yes' or 'No'
  final List<String> _tileSellerOptions = ['Select', 'Yes', 'No'];
  final TextEditingController tileStockController = TextEditingController();

  final List<GiftRow> giftRows = [];
  final List<String> _giftTypeOptions = ['Select', 'Gift A', 'Gift B', 'Gift C'];

  final _formKey = GlobalKey<FormState>();
  String selectedProcessType = 'Update';
  final List<String> _processTypeOptions = ['Add', 'Update', 'Delete'];

  String selectedParticipation = 'NA';
  final List<String> _participationOptions = ['Yes', 'No', 'NA'];

  String? selectedPendingIssue = 'No';
  final List<String> _pendingIssueOptions = ['Yes', 'No'];

  String? selectedPendingIssueType;
  final List<String> _pendingIssueTypeOptions = ['Select', 'Token', 'Scheme', 'Product', 'Other'];

  String? geoLatit, geoLongt, purchaserLatit, purchaserLongt, locationCapturedAddress, distanceExceptionReason;

  static const double bagToMtFactor = 0.05;

  @override
  void initState() {
    super.initState();
    _apiService.currentLoginIdM = widget.loginId;

    documentNoController.text = widget.docuNumb;
    areaCodeController.text = widget.areaCode;
    codeController.text = widget.retailerCode;
    nameController.text = widget.retailerName;

    geoLatit = widget.initialGeoLatit;
    geoLongt = widget.initialGeoLongt;
    purchaserLatit = widget.initialPurchaserLatit;
    purchaserLongt = widget.initialPurchaserLongt;

    reportDateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());
    orderExecDateController.text = DateFormat('yyyy-MM-dd').format(DateTime.now());

    _addOrderRow();
    _addGiftRow();
    _addMarketWcpSkuRow();

    if (widget.docuNumb.isNotEmpty) {
      _fetchDsrDetails(widget.docuNumb);
    }
  }

  void _addMarketWcpSkuRow() {
    setState(() {
      marketWcpSkuRows.add(MarketWcpSkuRow());
    });
  }

  void _removeMarketWcpSkuRow(int index) {
    if (marketWcpSkuRows.length > 1) {
      setState(() {
        marketWcpSkuRows[index].dispose();
        marketWcpSkuRows.removeAt(index);
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Cannot remove the last market WCP SKU row."),
          backgroundColor: Colors.orangeAccent,
        ),
      );
    }
  }

  Future<void> _fetchDsrDetails(String docuNumb) async {
    setState(() => _isFetchingDetails = true);
    try {
      final dsrData = await _apiService.getDsrDetails(docuNumb);
      if (dsrData is Map<String, dynamic>) {
        // Parsing logic (if needed)
        // _populateFormWithFetchedData(DsrActivityFullDto.fromJson(dsrData['data']));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fetching DSR details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isFetchingDetails = false);
    }
  }

  void _populateFormWithFetchedData(DsrActivityFullDto data) {
    setState(() {
      selectedProcessType = data.proctype.isNotEmpty ? data.proctype : 'Update';
      reportDateController.text = data.docuDate ?? '';

      purchaserTypeController.text = data.cusRtlFl ?? widget.retailerName;
      kycStatusController.text = data.kycStatus ?? '';
      marketNameController.text = data.marketName ?? '';
      selectedParticipation = data.displayContestParticipation ?? 'NA';
      selectedPendingIssue = data.pendingIssueStatus ?? 'No';
      selectedPendingIssueType = data.pendingIssueDetailType ?? 'Select';
      pendingIssueSpecifyTextController.text = data.pendingIssueSpecifyText ?? '';

      enrolWCController.text = data.enrolWCSlab ?? '';
      enrolWCPController.text = data.enrolWCPSlab ?? '';
      enrolVAPController.text = data.enrolVAPSlab ?? '';
      bwStockWCController.text = data.bwStockWC ?? '';
      bwStockWCPController.text = data.bwStockWCP ?? '';
      bwStockVAPController.text = data.bwStockVAP ?? '';

      if (data.brandsSellingWC is String) {
        _selectedBrandsWC = [data.brandsSellingWC as String];
      } else if (data.brandsSellingWC is List) {
        _selectedBrandsWC = List<String>.from(data.brandsSellingWC as List);
      } else {
        _selectedBrandsWC = [];
      }
      wcIndustryVolumeController.text = data.wcIndustryVolume ?? '';

      if (data.brandsSellingWCP is String) {
        _selectedBrandsWCP = [data.brandsSellingWCP as String];
      } else if (data.brandsSellingWCP is List) {
        _selectedBrandsWCP = List<String>.from(data.brandsSellingWCP as List);
      } else {
        _selectedBrandsWCP = [];
      }
      wcpIndustryVolumeController.text = data.wcpIndustryVolume ?? '';

      last3MonthsBwWCController.text = data.last3MonthsBwWC ?? '';
      last3MonthsBwWCPController.text = data.last3MonthsBwWCP ?? '';
      last3MonthsBwVAPController.text = data.last3MonthsBwVAP ?? '';
      currentMonthBwWCController.text = data.currentMonthBwWC ?? '';
      currentMonthBwWCPController.text = data.currentMonthBwWCP ?? '';
      currentMonthBwVAPController.text = data.currentMonthBwVAP ?? '';

      if (data.competitorSales != null && data.competitorSales!.isNotEmpty) {
        for (var saleDto in data.competitorSales!) {
          var existingRow = competitorSaleRows.firstWhere(
                (row) => row.brandName == (saleDto.competitorName ?? ''),
            orElse: () => CompetitorSaleRow(brandName: saleDto.competitorName ?? ''),
          );
          existingRow.wcQtyController.text = saleDto.wcQty ?? '';
          existingRow.wcpQtyController.text = saleDto.wcpQty ?? '';
          if (!competitorSaleRows.any((r) => r.brandName == existingRow.brandName)) {
            // If it’s a brand not already in the fixed list, you could add it:
            // competitorSaleRows.add(existingRow);
          }
        }
      }

      orderRows.clear();
      for (var item in data.orderItems ?? []) {
        final newRow = OrderRow(onQtyChanged: () => setState(() {}));
        newRow.productController.text = item.repoCatg ?? '';
        newRow.skuController.text = item.prodCode ?? '';
        newRow.qtyBagsController.text = item.quantityInBags?.toString() ?? '';
        orderRows.add(newRow);
      }
      if (orderRows.isEmpty) _addOrderRow();

      marketWcpSkuRows.clear();
      for (var item in data.marketWcpSkus ?? []) {
        final newRow = MarketWcpSkuRow();
        newRow.brandNameController.text = item.brandName ?? '';
        newRow.productCodeMarketController.text = item.prdCodMk ?? '';
        newRow.bPriceController.text = item.bPriceVl ?? '';
        newRow.cPriceController.text = item.cPriceVl ?? '';
        marketWcpSkuRows.add(newRow);
      }
      if (marketWcpSkuRows.isEmpty) _addMarketWcpSkuRow();

      orderExecDateController.text =
          data.orderExecDate ?? DateFormat('yyyy-MM-dd').format(DateTime.now());
      otherRemarksController.text = data.otherRemarks ?? '';

      giftRows.clear();
      for (var item in data.giftItems ?? []) {
        final newRow = GiftRow(giftType: item.giftTypeCode ?? 'Select');
        newRow.qtyController.text = item.quantity?.toString() ?? '';
        giftRows.add(newRow);
      }
      if (giftRows.isEmpty) _addGiftRow();

      selectedTileSeller = data.isTileAdhesiveSeller;
      tileStockController.text = data.tileAdhesiveStock ?? '';

      geoLatit = data.geoLatit ?? widget.initialGeoLatit;
      geoLongt = data.geoLongt ?? widget.initialGeoLongt;
      purchaserLatit = data.purchaserLatit ?? widget.initialPurchaserLatit;
      purchaserLongt = data.purchaserLongt ?? widget.initialPurchaserLongt;
      locationCapturedAddress = data.locationCapturedAddress;
      distanceExceptionReason = data.distanceExceptionReason;
    });
  }

  void _addOrderRow() {
    final newRow = OrderRow(onQtyChanged: () => setState(() {}));
    setState(() => orderRows.add(newRow));
  }

  void _removeOrderRow(int index) {
    if (orderRows.length > 1) {
      setState(() {
        orderRows[index].dispose();
        orderRows.removeAt(index);
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Cannot remove the last order row."),
          backgroundColor: Colors.orangeAccent,
        ),
      );
    }
  }

  void _addGiftRow() {
    setState(() => giftRows.add(GiftRow(giftType: 'Select')));
  }

  void _removeGiftRow(int index) {
    if (giftRows.length > 1) {
      setState(() {
        giftRows[index].dispose();
        giftRows.removeAt(index);
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Cannot remove the last gift row."),
          backgroundColor: Colors.orangeAccent,
        ),
      );
    }
  }

  void _removeMarketWcpSkuRowFromList(int index) {
    if (marketWcpSkuRows.length > 1) {
      setState(() {
        marketWcpSkuRows[index].dispose();
        marketWcpSkuRows.removeAt(index);
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Cannot remove the last market WCP SKU row."),
          backgroundColor: Colors.orangeAccent,
        ),
      );
    }
  }

  Future<void> _submitDsrFull() async {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fix errors in the form.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    setState(() => _isLoading = true);

    final dto = DsrActivityFullDto(
      loginIdM: widget.loginId,
      docuNumb: documentNoController.text,
      proctype: selectedProcessType,
      docuDate: reportDateController.text,
      dsrParam: "05",
      cusRtlFl: purchaserTypeController.text,
      cusRtlCd: codeController.text,
      areaCode: areaCodeController.text,
      cusRtlNm: nameController.text,
      kycStatus: kycStatusController.text,
      marketName: marketNameController.text.trim().isNotEmpty ? marketNameController.text.trim() : null,
      displayContestParticipation: selectedParticipation == 'NA' ? null : selectedParticipation,
      pendingIssueStatus: selectedPendingIssue == 'No' ? null : selectedPendingIssue,
      pendingIssueDetailType: selectedPendingIssue == 'Yes'
          ? (selectedPendingIssueType == 'Select' ? null : selectedPendingIssueType)
          : null,
      pendingIssueSpecifyText: selectedPendingIssue == 'Yes' &&
          pendingIssueSpecifyTextController.text.trim().isNotEmpty
          ? pendingIssueSpecifyTextController.text.trim()
          : null,
      enrolWCSlab: enrolWCController.text.trim().isNotEmpty ? enrolWCController.text.trim() : null,
      enrolWCPSlab: enrolWCPController.text.trim().isNotEmpty ? enrolWCPController.text.trim() : null,
      enrolVAPSlab: enrolVAPController.text.trim().isNotEmpty ? enrolVAPController.text.trim() : null,
      bwStockWC: bwStockWCController.text.trim().isNotEmpty ? bwStockWCController.text.trim() : null,
      bwStockWCP: bwStockWCPController.text.trim().isNotEmpty ? bwStockWCPController.text.trim() : null,
      bwStockVAP: bwStockVAPController.text.trim().isNotEmpty ? bwStockVAPController.text.trim() : null,
      brandsSellingWC: _selectedBrandsWC.isNotEmpty ? _selectedBrandsWC.join(',') : null,
      wcIndustryVolume: wcIndustryVolumeController.text.trim().isNotEmpty
          ? wcIndustryVolumeController.text.trim()
          : null,
      brandsSellingWCP: _selectedBrandsWCP.isNotEmpty ? _selectedBrandsWCP.join(',') : null,
      wcpIndustryVolume: wcpIndustryVolumeController.text.trim().isNotEmpty
          ? wcpIndustryVolumeController.text.trim()
          : null,
      last3MonthsBwWC: last3MonthsBwWCController.text.trim().isNotEmpty ? last3MonthsBwWCController.text.trim() : null,
      last3MonthsBwWCP: last3MonthsBwWCPController.text.trim().isNotEmpty ? last3MonthsBwWCPController.text.trim() : null,
      last3MonthsBwVAP: last3MonthsBwVAPController.text.trim().isNotEmpty ? last3MonthsBwVAPController.text.trim() : null,
      currentMonthBwWC: currentMonthBwWCController.text.trim().isNotEmpty ? currentMonthBwWCController.text.trim() : null,
      currentMonthBwWCP: currentMonthBwWCPController.text.trim().isNotEmpty ? currentMonthBwWCPController.text.trim() : null,
      currentMonthBwVAP: currentMonthBwVAPController.text.trim().isNotEmpty ? currentMonthBwVAPController.text.trim() : null,
      competitorSales: competitorSaleRows
          .map((row) => row.toDto())
          .where((dto) => dto.wcQty != null || dto.wcpQty != null)
          .toList(),
      orderItems: orderRows
          .map((row) => row.toDto())
          .where((dto) =>
      (dto.prodCode?.isNotEmpty ?? false) &&
          ((dto.quantityInBags ?? 0) > 0 || (dto.quantityInMT ?? 0) > 0))
          .toList(),
      marketWcpSkus: marketWcpSkuRows
          .map((row) => row.toDto())
          .where((dto) =>
      (dto.brandName?.isNotEmpty ?? false) ||
          (dto.prdCodMk?.isNotEmpty ?? false))
          .toList(),
      orderExecDate: orderExecDateController.text.trim().isNotEmpty
          ? orderExecDateController.text.trim()
          : null,
      otherRemarks: otherRemarksController.text.trim().isNotEmpty
          ? otherRemarksController.text.trim()
          : null,
      giftItems: giftRows
          .map((row) => row.toDto())
          .where((dto) =>
      (dto.giftTypeCode?.isNotEmpty ?? false) && (dto.quantity ?? 0) > 0)
          .toList(),
      isTileAdhesiveSeller: selectedTileSeller == 'Select' ? null : selectedTileSeller,
      tileAdhesiveStock: tileStockController.text.trim().isNotEmpty
          ? tileStockController.text.trim()
          : null,
      geoLatit: geoLatit,
      geoLongt: geoLongt,
      purchaserLatit: purchaserLatit,
      purchaserLongt: purchaserLongt,
      locationCapturedAddress: locationCapturedAddress,
      distanceExceptionReason: distanceExceptionReason,
    );

    try {
      await _apiService.submitFullDsr(dto);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('DSR submitted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit DSR: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    documentNoController.dispose();
    purchaserTypeController.dispose();
    areaCodeController.dispose();
    codeController.dispose();
    nameController.dispose();
    kycStatusController.dispose();
    reportDateController.dispose();
    marketNameController.dispose();
    pendingIssueSpecifyTextController.dispose();
    enrolWCController.dispose();
    enrolWCPController.dispose();
    enrolVAPController.dispose();
    bwStockWCController.dispose();
    bwStockWCPController.dispose();
    bwStockVAPController.dispose();
    last3MonthsBwWCController.dispose();
    last3MonthsBwWCPController.dispose();
    last3MonthsBwVAPController.dispose();
    currentMonthBwWCController.dispose();
    currentMonthBwWCPController.dispose();
    currentMonthBwVAPController.dispose();
    for (var row in competitorSaleRows) row.dispose();
    for (var row in marketWcpSkuRows) row.dispose();
    wcIndustryVolumeController.dispose();
    wcpIndustryVolumeController.dispose();
    for (var row in orderRows) row.dispose();
    orderExecDateController.dispose();
    otherRemarksController.dispose();
    tileStockController.dispose();
    for (var row in giftRows) row.dispose();
    super.dispose();
  }

  Future<void> _pickDate(BuildContext context, TextEditingController controller) async {
    DateTime initialDate = DateTime.now();
    if (controller.text.isNotEmpty) {
      try {
        initialDate = DateFormat('yyyy-MM-dd').parse(controller.text);
      } catch (_) {}
    }
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      setState(() => controller.text = DateFormat('yyyy-MM-dd').format(picked));
    }
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    bool readOnly = false,
    VoidCallback? onTap,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    FocusNode? focusNode,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        readOnly: readOnly,
        onTap: onTap,
        keyboardType: keyboardType,
        focusNode: focusNode,
        decoration: AppTheme.textFieldInputDecoration(
          hintText: labelText,
          labelText: labelText,
        ),
        validator: validator ??
                (val) =>
            (val == null || val.trim().isEmpty) && !readOnly && !labelText.contains("(Optional)")
                ? '$labelText is required'
                : null,
      ),
    );
  }

  Widget _buildDropdownFormField({
    required String? currentValue,
    required List<String> items,
    required String labelText,
    required ValueChanged<String?> onChanged,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<String>(
        value: currentValue,
        decoration: AppTheme.dropdownDecoration(hintText: labelText),
        items: items.map((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value),
          );
        }).toList(),
        onChanged: onChanged,
        validator: validator ??
                (val) =>
            (val == null || val == 'Select') ? '$labelText is required' : null,
        isExpanded: true,
      ),
    );
  }

  Widget _buildMultiSelectChipGroup({
    required String title,
    required List<String> allOptions,
    required List<String> selectedOptions,
    required ValueChanged<List<String>> onSelectionChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTheme.buildLabel(title),
        const SizedBox(height: 4),
        Wrap(
          spacing: 8.0,
          runSpacing: 0.0,
          children: allOptions.map((option) {
            final isSelected = selectedOptions.contains(option);
            return FilterChip(
              label: Text(option, style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.textColor,
              )),
              selected: isSelected,
              onSelected: (bool selected) {
                setState(() {
                  if (selected) {
                    selectedOptions.add(option);
                  } else {
                    selectedOptions.remove(option);
                  }
                  onSelectionChanged(selectedOptions);
                });
              },
              backgroundColor:
              isSelected ? AppTheme.primaryColor.withOpacity(0.8) : Colors.grey[200],
              selectedColor: AppTheme.primaryColor,
              checkmarkColor: Colors.white,
              showCheckmark: true,
              labelPadding: const EdgeInsets.symmetric(horizontal: 8.0),
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCompetitorSalesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTheme.buildLabel('Last 3 Months Average (Competitors)*'),
        const SizedBox(height: 8),
        Table(
          border: TableBorder.all(color: Colors.grey.shade300),
          columnWidths: const {
            0: FlexColumnWidth(2.5),
            1: FlexColumnWidth(1.5),
            2: FlexColumnWidth(1.5)
          },
          children: [
            TableRow(
              decoration: BoxDecoration(color: Colors.grey.shade100),
              children: const [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Brand',
                      style: TextStyle(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('WC Qty',
                      style: TextStyle(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('WCP Qty',
                      style: TextStyle(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center),
                ),
              ],
            ),
            ...competitorSaleRows.map((rowState) {
              int index = competitorSaleRows.indexOf(rowState);
              return TableRow(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: TextFormField(
                      controller: TextEditingController(text: rowState.brandName),
                      readOnly: true,
                      decoration: const InputDecoration(border: InputBorder.none),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: TextFormField(
                      controller: rowState.wcQtyController,
                      keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                      decoration: const InputDecoration(
                        hintText: '0.00',
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                      ),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: TextFormField(
                      controller: rowState.wcpQtyController,
                      keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                      decoration: const InputDecoration(
                        hintText: '0.00',
                        border: InputBorder.none,
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                      ),
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  Widget _buildMarketWcpSkuSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            AppTheme.buildLabel('Market WCP (Highest Selling SKU)'),
            if (marketWcpSkuRows.length < 5)
              IconButton(
                icon: const Icon(Icons.add_circle, color: AppTheme.primaryColor),
                onPressed: _addMarketWcpSkuRow,
              )
          ],
        ),
        const SizedBox(height: 8),
        if (marketWcpSkuRows.isEmpty) const Text("No WCP SKUs added yet."),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: marketWcpSkuRows.length,
          itemBuilder: (context, index) {
            final row = marketWcpSkuRows[index];
            return Card(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    Row(children: [
                      Expanded(
                        child: AppTheme.buildTextField(
                          'Brand Name',
                          controller: row.brandNameController,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: AppTheme.buildTextField(
                          'Product Code',
                          controller: row.productCodeMarketController,
                        ),
                      ),
                    ]),
                    const SizedBox(height: 8),
                    Row(children: [
                      Expanded(
                        child: AppTheme.buildTextField(
                          'Price - B',
                          controller: row.bPriceController,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: AppTheme.buildTextField(
                          'Price - C',
                          controller: row.cPriceController,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      if (marketWcpSkuRows.length > 1)
                        IconButton(
                          icon: const Icon(Icons.remove_circle_outline, color: Colors.red),
                          onPressed: () => _removeMarketWcpSkuRowFromList(index),
                        ),
                    ]),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('DSR: ${widget.docuNumb}'),
        backgroundColor: AppTheme.primaryColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isFetchingDetails
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppTheme.buildSectionCard(
                title: 'Basic Info',
                icon: Icons.info_outline,
                children: [
                  _buildDropdownFormField(
                    labelText: 'Process Type *',
                    currentValue: selectedProcessType,
                    items: _processTypeOptions,
                    onChanged: (val) => setState(() => selectedProcessType = val!),
                  ),
                  _buildTextFormField(
                    controller: documentNoController,
                    labelText: 'Document No',
                    readOnly: true,
                  ),
                  _buildTextFormField(
                    controller: purchaserTypeController,
                    labelText: 'Purchaser / Retailer Type',
                    readOnly: true,
                  ),
                  _buildTextFormField(
                    controller: areaCodeController,
                    labelText: 'Area Code',
                    readOnly: true,
                  ),
                  _buildTextFormField(
                    controller: codeController,
                    labelText: 'Retailer Code',
                    readOnly: true,
                  ),
                  _buildTextFormField(
                    controller: nameController,
                    labelText: 'Retailer Name',
                    readOnly: true,
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Visit Details',
                icon: Icons.person_pin_circle_outlined,
                children: [
                  _buildTextFormField(
                    controller: kycStatusController,
                    labelText: 'KYC Status',
                    readOnly: true,
                  ),
                  _buildTextFormField(
                    controller: reportDateController,
                    labelText: 'Report Date *',
                    readOnly: true,
                    onTap: () => _pickDate(context, reportDateController),
                  ),
                  _buildTextFormField(
                    controller: marketNameController,
                    labelText: 'Market Name *',
                  ),
                  _buildDropdownFormField(
                    labelText: 'Participation of Display Contest *',
                    currentValue: selectedParticipation,
                    items: _participationOptions,
                    onChanged: (val) =>
                        setState(() => selectedParticipation = val!),
                  ),
                  _buildDropdownFormField(
                    labelText: 'Any Pending Issues (Yes / No) *',
                    currentValue: selectedPendingIssue,
                    items: _pendingIssueOptions,
                    onChanged: (val) => setState(() => selectedPendingIssue = val!),
                  ),
                  if (selectedPendingIssue == 'Yes') ...[
                    _buildDropdownFormField(
                      labelText: 'Pending Issue Details *',
                      currentValue: selectedPendingIssueType,
                      items: _pendingIssueTypeOptions,
                      onChanged: (val) =>
                          setState(() => selectedPendingIssueType = val),
                      validator: (val) =>
                      (val == null || val == 'Select') ? 'Required' : null,
                    ),
                    _buildTextFormField(
                      controller: pendingIssueSpecifyTextController,
                      labelText: 'Specify Issue (if Other/Details)',
                    ),
                  ],
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Stock & Enrollment',
                icon: Icons.inventory_2_outlined,
                children: [
                  AppTheme.buildLabel('Enrolment Slab (in MT)*'),
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextFormField(
                          controller: enrolWCController,
                          labelText: 'WC',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: enrolWCPController,
                          labelText: 'WCP',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: enrolVAPController,
                          labelText: 'VAP',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('BW Stocks Availability (in MT)*'),
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextFormField(
                          controller: bwStockWCController,
                          labelText: 'WC',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: bwStockWCPController,
                          labelText: 'WCP',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: bwStockVAPController,
                          labelText: 'VAP',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Brand & Industry Volume',
                icon: Icons.storefront_outlined,
                children: [
                  _buildMultiSelectChipGroup(
                    title: 'Brands selling - WC*',
                    allOptions: _allBrandsWC,
                    selectedOptions: _selectedBrandsWC,
                    onSelectionChanged: (selected) =>
                        setState(() => _selectedBrandsWC = selected),
                  ),
                  _buildTextFormField(
                    controller: wcIndustryVolumeController,
                    labelText: 'WC Industry Volume (MT)',
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  _buildMultiSelectChipGroup(
                    title: 'Brands selling - WCP*',
                    allOptions: _allBrandsWCP,
                    selectedOptions: _selectedBrandsWCP,
                    onSelectionChanged: (selected) =>
                        setState(() => _selectedBrandsWCP = selected),
                  ),
                  _buildTextFormField(
                    controller: wcpIndustryVolumeController,
                    labelText: 'WCP Industry Volume (MT)',
                    keyboardType: TextInputType.number,
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Sales Averages (BW)',
                icon: Icons.insights_outlined,
                children: [
                  AppTheme.buildLabel('Last 3 Months Average - BW (in MT)'),
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextFormField(
                          controller: last3MonthsBwWCController,
                          labelText: 'WC',
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: last3MonthsBwWCPController,
                          labelText: 'WCP',
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: last3MonthsBwVAPController,
                          labelText: 'VAP',
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Current Months - BW (in MT)'),
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextFormField(
                          controller: currentMonthBwWCController,
                          labelText: 'WC',
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: currentMonthBwWCPController,
                          labelText: 'WCP',
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildTextFormField(
                          controller: currentMonthBwVAPController,
                          labelText: 'VAP',
                          keyboardType: TextInputType.number,
                          readOnly: true,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Competitor Sales',
                icon: Icons.assessment_outlined,
                children: [
                  _buildCompetitorSalesSection(),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Order Booking',
                icon: Icons.shopping_cart_checkout_outlined,
                children: [
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: orderRows.length,
                    itemBuilder: (context, index) {
                      final row = orderRows[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        color: Colors.grey[50],
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            children: [
                              Row(children: [
                                Expanded(
                                  child: _buildTextFormField(
                                    controller: row.productController,
                                    labelText: 'Product (Category)',
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: _buildTextFormField(
                                    controller: row.skuController,
                                    labelText: 'Product (SKU)',
                                  ),
                                ),
                              ]),
                              const SizedBox(height: 8),
                              Row(children: [
                                Expanded(
                                  child: _buildTextFormField(
                                    controller: row.qtyBagsController,
                                    labelText: 'Qty (Bags)',
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Qty (MT): ${((double.tryParse(row.qtyBagsController.text.trim()) ?? 0) * bagToMtFactor).toStringAsFixed(3)}',
                                    style: const TextStyle(
                                        fontSize: 14, fontWeight: FontWeight.w500),
                                  ),
                                ),
                                if (orderRows.length > 1)
                                  IconButton(
                                    icon: const Icon(Icons.remove_circle_outline,
                                        color: Colors.red),
                                    onPressed: () => _removeOrderRow(index),
                                  ),
                              ]),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 8),
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _addOrderRow,
                      icon: const Icon(Icons.add),
                      label: const Text("Add Order Item"),
                      style: AppTheme.elevatedButtonStyle(
                        bgColor: AppTheme.primaryColor.withOpacity(0.8),
                      ),
                    ),
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Market WCP SKUs',
                icon: Icons.local_offer_outlined,
                children: [
                  _buildMarketWcpSkuSection(),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Execution & Remarks',
                icon: Icons.edit_calendar_outlined,
                children: [
                  _buildTextFormField(
                    controller: orderExecDateController,
                    labelText: 'Order Execution Date',
                    readOnly: true,
                    onTap: () => _pickDate(context, orderExecDateController),
                  ),
                  _buildTextFormField(
                    controller: otherRemarksController,
                    labelText: 'Any Other Remarks',
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Gift Distribution',
                icon: Icons.card_giftcard_outlined,
                children: [
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: giftRows.length,
                    itemBuilder: (context, index) {
                      final row = giftRows[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        color: Colors.grey[50],
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: _buildDropdownFormField(
                                  labelText: 'Gift Type',
                                  currentValue: row.giftType,
                                  items: _giftTypeOptions,
                                  onChanged: (val) =>
                                      setState(() => row.giftType = val),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: _buildTextFormField(
                                  controller: row.qtyController,
                                  labelText: 'Quantity',
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              if (giftRows.length > 1)
                                IconButton(
                                  icon: const Icon(Icons.remove_circle_outline,
                                      color: Colors.red),
                                  onPressed: () => _removeGiftRow(index),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 8),
                  Center(
                    child: ElevatedButton.icon(
                      onPressed: _addGiftRow,
                      icon: const Icon(Icons.add),
                      label: const Text("Add Gift Item"),
                      style: AppTheme.elevatedButtonStyle(
                        bgColor: AppTheme.primaryColor.withOpacity(0.8),
                      ),
                    ),
                  ),
                ],
              ),

              AppTheme.buildSectionCard(
                title: 'Tile Adhesives',
                icon: Icons.layers_outlined,
                children: [
                  _buildDropdownFormField(
                    labelText: 'Is this Tile Adhesives seller?',
                    currentValue: selectedTileSeller,
                    items: _tileSellerOptions,
                    onChanged: (val) =>
                        setState(() => selectedTileSeller = val == 'Select' ? null : val),
                    validator: (val) => (val == null) ? 'Required' : null,
                  ),
                  if (selectedTileSeller == 'Yes')
                    _buildTextFormField(
                      controller: tileStockController,
                      labelText: 'Tile Adhesive Stock (MT)',
                      keyboardType: TextInputType.number,
                    ),
                ],
              ),

              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _isLoading ? null : _submitDsrFull,
                style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                child: _isLoading
                    ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                )
                    : const Text(
                  'Submit Full DSR',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
