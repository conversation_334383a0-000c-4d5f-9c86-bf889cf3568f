import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
// No longer need direct http import if ApiService handles all
// import 'package:http/http.dart' as http;

import 'dsr_entry.dart';
import '../theme/app_theme.dart';
import '../models.dart'; // Import your models
import '../api_service.dart'; // Import your ApiService

class BtlActivites extends StatefulWidget {
  const BtlActivites({super.key});

  @override
  State<BtlActivites> createState() => _BtlActivitesState();
}

class _BtlActivitesState extends State<BtlActivites> {
  final ApiService _apiService = ApiService();
  bool _isSubmitting = false;

  String? _processItem = 'Select';
  final List<String> _processdropdownItems = ['Select', 'Add', 'Update'];
  String _activityTypeItem = 'Select';
  final List<String> _activityTypedropdownItems = [
    'Select', 'Retailer Meet', 'Stokiest Meet', 'Painter Meet',
    'Architect Meet', 'Counter Meet', 'Painter Training Program', 'Other BTL Activities',
  ];

  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _reportDateController = TextEditingController();
  DateTime? _selectedDate;
  DateTime? _selectedReportDate;
  final TextEditingController _noOfParticipantsController = TextEditingController();
  final TextEditingController _townController = TextEditingController();
  final TextEditingController _learningsController = TextEditingController();

  final List<File?> _selectedImages = [null]; // Initialize with one slot
  final List<int> _uploadRows = [0]; // To manage UI rows for images

  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();


  @override
  void initState() {
    super.initState();
    // _apiService.currentLoginIdM = "your_actual_login_id"; // Set actual login ID
    _selectedDate = DateTime.now();
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);
    _selectedReportDate = DateTime.now();
    _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
  }

  @override
  void dispose() {
    _dateController.dispose();
    _reportDateController.dispose();
    _noOfParticipantsController.dispose();
    _townController.dispose();
    _learningsController.dispose();
    super.dispose();
  }

  Future<void> _pickDate(bool isSubmissionDate) async {
    final now = DateTime.now();
    DateTime? initialDate;
    if(isSubmissionDate) {
      initialDate = _selectedDate ?? now;
    } else {
      initialDate = _selectedReportDate ?? now;
    }

    final picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      setState(() {
        if(isSubmissionDate) {
          _selectedDate = picked;
          _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
        } else {
          _selectedReportDate = picked;
          _reportDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        }
      });
    }
  }

  Future<void> _pickImage(int index) async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _selectedImages[index] = File(pickedFile.path);
      });
    }
  }

  void _addImageField() {
    if (_uploadRows.length >= 3) return;
    setState(() {
      _uploadRows.add(_uploadRows.length);
      _selectedImages.add(null);
    });
  }

  void _removeImageField(int index) { // Modified to remove specific index
    if (_uploadRows.length <= 1) return;
    setState(() {
      if (index < _uploadRows.length && index < _selectedImages.length) {
        _uploadRows.removeAt(index);
        _selectedImages.removeAt(index);
        // Re-index _uploadRows if it's used for display numbering
        for(int i=0; i < _uploadRows.length; i++) {
          _uploadRows[i] = i;
        }
      }
      if (_uploadRows.isEmpty) { // Ensure at least one row remains if needed
        _uploadRows.add(0);
        _selectedImages.add(null);
      }
    });
  }


  void _showImageDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
          child: InteractiveViewer(
            panEnabled: false,
            boundaryMargin: const EdgeInsets.all(20),
            minScale: 0.5,
            maxScale: 2,
            child: Image.file(imageFile, fit: BoxFit.contain),
          )
      ),
    );
  }


  Future<String> _fileToBase64(File? file) async {
    if (file == null) return '';
    final bytes = await file.readAsBytes();
    return base64Encode(bytes);
  }

  Future<void> _submitForm({bool exitAfter = false}) async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isSubmitting = true);

    final imgfirst = await _fileToBase64(_selectedImages.length > 0 ? _selectedImages[0] : null);
    final imgscndd = await _fileToBase64(_selectedImages.length > 1 ? _selectedImages[1] : null);
    final imgthird = await _fileToBase64(_selectedImages.length > 2 ? _selectedImages[2] : null);

    final dto = BtlActivityDto(
      proctype: _processItem == 'Select' ? 'Add' : _processItem!,
      submdate: _dateController.text,
      repodate: _reportDateController.text,
      actitype: _activityTypeItem == 'Select' ? '' : _activityTypeItem,
      numpartc: _noOfParticipantsController.text,
      townname: _townController.text,
      learnnng: _learningsController.text,
      imgfirst: imgfirst,
      imgscndd: imgscndd,
      imgthird: imgthird,
    );

    try {
      // IMPORTANT: Replace "/specificactivity/btl" with your actual endpoint
      // OR refactor to use DsrActivityFullDto with _apiService.submitFullDsr()
      await _apiService.submitBtlActivity(dto, "/dsractivity/btlactivity"); // Placeholder endpoint

      if(mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('BTL Activity submitted successfully!'), backgroundColor: Colors.green),
        );
        if (exitAfter) {
          Navigator.of(context).pop();
        } else {
          _resetForm();
        }
      }
    } catch (e) {
      if(mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Submission failed: $e'), backgroundColor: Colors.redAccent),
        );
      }
    } finally {
      if(mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _resetForm() {
    _formKey.currentState!.reset();
    setState(() {
      _processItem = 'Select';
      _selectedDate = DateTime.now();
      _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      _selectedReportDate = DateTime.now();
      _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
      _activityTypeItem = 'Select';
      _noOfParticipantsController.clear();
      _townController.clear();
      _learningsController.clear();

      _uploadRows.clear();
      _selectedImages.clear();
      _uploadRows.add(0); // Add first row back
      _selectedImages.add(null); // Add placeholder for first image
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pushReplacement( // Or push if you want to keep it in stack
              context,
              MaterialPageRoute(builder: (context) => const DsrEntry()),
            );
          },
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 22),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('BTL Activities', style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.white)),
            Text('Daily Sales Report Entry', style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.white70)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline, color: Colors.white, size: 24),
            onPressed: () { /* Help action */ },
          ),
        ],
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              AppTheme.buildSectionCard(
                title: 'Process',
                icon: Icons.settings_outlined,
                children: [
                  AppTheme.buildLabel('Process Type'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _processItem,
                    decoration: AppTheme.dropdownDecoration(hintText: 'Select Process'),
                    isExpanded: true,
                    items: _processdropdownItems.map((item) => DropdownMenuItem<String>(value: item, child: Text(item, style: const TextStyle(fontSize: 14)))).toList(),
                    onChanged: (newValue) {
                      if (newValue != null) setState(() => _processItem = newValue);
                    },
                    validator: (value) => (value == null || value == 'Select') ? 'Please select a process' : null,
                  ),
                ],
              ),
              AppTheme.buildSectionCard(
                title: 'Date Information',
                icon: Icons.date_range_outlined,
                children: [
                  AppTheme.buildLabel('Submission Date'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(context, _dateController, () => _pickDate(true), 'Select Submission Date', initialDate: _selectedDate),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Report Date'),
                  const SizedBox(height: 8),
                  AppTheme.buildDateField(context, _reportDateController, () => _pickDate(false), 'Select Report Date', initialDate: _selectedReportDate),
                ],
              ),
              AppTheme.buildSectionCard(
                title: 'BTL Activity Details',
                icon: Icons.campaign_outlined,
                children: [
                  AppTheme.buildLabel('Type Of Activity'),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _activityTypeItem,
                    decoration: AppTheme.dropdownDecoration(hintText: 'Select Activity Type'),
                    isExpanded: true,
                    items: _activityTypedropdownItems.map((item) => DropdownMenuItem<String>(value: item, child: Text(item, style: const TextStyle(fontSize: 14)))).toList(),
                    onChanged: (newValue) {
                      if (newValue != null) setState(() => _activityTypeItem = newValue);
                    },
                    validator: (value) => (value == null || value == 'Select') ? 'Please select a type of activity' : null,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('No. Of Participants'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField('Enter number of participants', controller: _noOfParticipantsController, keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) return 'Please enter number of participants';
                      if (int.tryParse(value) == null) return 'Invalid number';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Town in Which Activity Conducted'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField('Enter town', controller: _townController,
                    validator: (value) => (value == null || value.isEmpty) ? 'Please enter town' : null,
                  ),
                  const SizedBox(height: 16),
                  AppTheme.buildLabel('Learning\'s From Activity'),
                  const SizedBox(height: 8),
                  AppTheme.buildTextField('Enter your learnings', controller: _learningsController, maxLines: 3,
                    validator: (value) => (value == null || value.isEmpty) ? 'Please enter your learnings' : null,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Container(
                margin: const EdgeInsets.only(top: 8, bottom: 16),
                padding: const EdgeInsets.all(16),
                decoration: AppTheme.cardDecoration,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(children: [ const Icon(Icons.photo_library_rounded, color: AppTheme.primaryColor, size: 24), const SizedBox(width: 8), Text('Supporting Documents', style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppTheme.primaryColor))]),
                    const SizedBox(height: 4),
                    Text('Upload images related to your activity', style: Theme.of(context).textTheme.bodyMedium),
                    const SizedBox(height: 16),
                    ...List.generate(_uploadRows.length, (index) {
                      final i = _uploadRows[index];
                      return Container( /* ... existing image row UI ... */
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: _selectedImages[index] != null ? Colors.green.shade200 : Colors.grey.shade200, width: 1.5),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(children: [
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                decoration: BoxDecoration(color: AppTheme.primaryColor.withOpacity(0.1), borderRadius: BorderRadius.circular(20)),
                                child: Text('Document ${index + 1}', style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 14)),
                              ),
                              const Spacer(),
                              if (_selectedImages[index] != null)
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                  decoration: BoxDecoration(color: Colors.green.shade100, borderRadius: BorderRadius.circular(20)),
                                  child: const Row(mainAxisSize: MainAxisSize.min, children: [ Icon(Icons.check_circle, color: Colors.green, size: 16), SizedBox(width: 4), Text('Uploaded', style: TextStyle(color: Colors.green, fontWeight: FontWeight.w500, fontSize: 14))]),
                                ),
                            ]),
                            const SizedBox(height:16),
                            if (_selectedImages[index] != null)
                              GestureDetector(
                                onTap: () => _showImageDialog(_selectedImages[index]!),
                                child: Container(
                                  height: 120, width: double.infinity, margin: const EdgeInsets.only(bottom:16),
                                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), image: DecorationImage(image: FileImage(_selectedImages[index]!), fit: BoxFit.cover)),
                                  child: Align(alignment: Alignment.topRight, child: Container(margin: const EdgeInsets.all(8), padding: const EdgeInsets.all(4), decoration: BoxDecoration(color: Colors.black.withOpacity(0.6), shape: BoxShape.circle), child: const Icon(Icons.zoom_in, color: Colors.white, size: 20))),
                                ),
                              ),
                            Row(children: [
                              Expanded(child: ElevatedButton.icon(onPressed: () => _pickImage(index), icon: Icon(_selectedImages[index] != null ? Icons.refresh:Icons.upload_file, size:18), label: Text(_selectedImages[index] != null?'Replace':'Upload'), style: AppTheme.elevatedButtonStyle(bgColor: _selectedImages[index] != null ? Colors.amber.shade600 : AppTheme.primaryColor))),
                              if (_selectedImages.length > 1 && index < _selectedImages.length) // Show remove only if more than 1 and valid index
                                Padding(
                                  padding: const EdgeInsets.only(left: 8),
                                  child: ElevatedButton.icon(
                                    onPressed: () => _removeImageField(index), // Pass index to remove
                                    icon: const Icon(Icons.remove_circle_outline, size: 20),
                                    label: const Text('Remove'),
                                    style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.dangerButtonColor),
                                  ),
                                ),
                            ]),
                          ],
                        ),
                      );
                    }),
                    if (_uploadRows.length < 3)
                      Center(
                        child: ElevatedButton.icon(
                          onPressed: _addImageField,
                          icon: const Icon(Icons.add_photo_alternate, size: 20),
                          label: const Text('Add Image'),
                          style: AppTheme.elevatedButtonStyle(),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ElevatedButton(
                    onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: false),
                    style: AppTheme.elevatedButtonStyle(),
                    child: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & New'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _isSubmitting ? null : () => _submitForm(exitAfter: true),
                    style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                    child: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & Exit'),
                  ),
                  const SizedBox(height: 16),
                  OutlinedButton.icon(
                    onPressed: () { debugPrint('View Submitted Data button pressed'); },
                    icon: const Icon(Icons.visibility_outlined),
                    label: const Text('Click to see Submitted Data'),
                    style: AppTheme.outlinedButtonStyle(),
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}