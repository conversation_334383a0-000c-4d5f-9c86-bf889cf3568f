// lib/theme/app_theme.dart
import 'package:flutter/material.dart';

class AppTheme {
  static const Color primaryColor = Color(0xFF007AFF); // Example Blue
  static const Color scaffoldBackgroundColor = Color(0xFFF0F2F5);
  static const Color cardColor = Colors.white;
  static const Color textColor = Color(0xFF333333);
  static const Color hintColor = Color(0xFF9E9E9E);
  static const Color successColor = Colors.green;
  static const Color dangerButtonColor = Colors.redAccent;

  static final ThemeData lightTheme = ThemeData(
    primaryColor: primaryColor,
    scaffoldBackgroundColor: scaffoldBackgroundColor,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      secondary: Colors.blueAccent,
      surface: cardColor,
      background: scaffoldBackgroundColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: textColor,
      onBackground: textColor,
      error: Colors.redAccent,
      onError: Colors.white,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryColor,
      elevation: 0,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    textTheme: TextTheme(
      headlineSmall:
      TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: textColor),
      titleLarge:
      TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: textColor),
      bodyMedium:
      TextStyle(fontSize: 14, color: textColor.withAlpha(179)), // 0.7 opacity
      labelLarge:
      TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: primaryColor),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.grey[100],
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryColor, width: 1.5),
      ),
      labelStyle: TextStyle(color: textColor.withAlpha(153)), // 0.6 opacity
      hintStyle: TextStyle(color: hintColor),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 2,
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor, width: 1.5),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    // === CHANGED HERE ===
    cardTheme: CardThemeData(
      elevation: 1,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      color: cardColor,
    ),
    // ====================
  );

  static BoxDecoration get cardDecoration => BoxDecoration(
    color: cardColor,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withAlpha(26), // 0.1 opacity
        spreadRadius: 1,
        blurRadius: 5,
        offset: const Offset(0, 2),
      ),
    ],
    border: Border.all(color: Colors.grey.shade200),
  );

  static BoxDecoration textFieldBoxDecoration() => BoxDecoration(
    boxShadow: [
      BoxShadow(
        color: Colors.grey.withAlpha(13), // 0.05 opacity
        spreadRadius: 0.5,
        blurRadius: 2,
        offset: const Offset(0, 1),
      ),
    ],
    borderRadius: BorderRadius.circular(8),
  );

  static InputDecoration textFieldInputDecoration({
    String? hintText,
    String? labelText,
    IconData? prefixIconData,
    int? maxLines,
  }) =>
      InputDecoration(
        hintText: hintText,
        labelText: labelText,
        hintStyle: TextStyle(color: hintColor, fontSize: 15),
        labelStyle: const TextStyle(color: textColor, fontSize: 15),
        prefixIcon: prefixIconData != null
            ? Icon(
          prefixIconData,
          color: primaryColor.withAlpha(179), // 0.7 opacity
          size: 20,
        )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryColor, width: 1.5),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        isDense: true,
      );

  static InputDecoration dropdownDecoration({String? hintText}) => InputDecoration(
    hintText: hintText ?? 'Select',
    filled: true,
    fillColor: Colors.grey[50], // Lighter fill for dropdowns
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide.none,
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: primaryColor, width: 1.5),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    isCollapsed: true,
  );

  static ButtonStyle elevatedButtonStyle({
    Color? bgColor,
    Color? fgColor,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: bgColor ?? primaryColor,
      foregroundColor: fgColor ?? Colors.white,
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 2,
    );
  }

  static ButtonStyle outlinedButtonStyle({
    Color? fgColor,
    Color? borderColor,
  }) {
    return OutlinedButton.styleFrom(
      foregroundColor: fgColor ?? primaryColor,
      side: BorderSide(color: borderColor ?? primaryColor, width: 1.5),
      padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
      textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  // --- STUBS for your AppTheme methods ---

  static Widget buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
    EdgeInsetsGeometry? padding,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 20),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 24),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const Divider(height: 24, thickness: 1),
            ...children,
          ],
        ),
      ),
    );
  }

  static Widget buildLabel(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6.0),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w600,
          color: AppTheme.textColor,
        ),
      ),
    );
  }

  static Widget buildDateField(
      BuildContext context,
      TextEditingController controller,
      VoidCallback onTap,
      String hintText, {
        DateTime? initialDate,
        String? Function(String?)? validator,
      }) {
    return TextFormField(
      controller: controller,
      readOnly: true,
      decoration: AppTheme
          .textFieldInputDecoration(hintText: hintText)
          .copyWith(
        suffixIcon: IconButton(
          icon: const Icon(Icons.calendar_today,
              color: AppTheme.primaryColor),
          onPressed: onTap,
        ),
      ),
      onTap: onTap,
      validator: validator ??
              (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a date';
            }
            return null;
          },
    );
  }

  static Widget buildTextField(
      String hintText, {
        required TextEditingController controller,
        TextInputType? keyboardType,
        int maxLines = 1,
        String? Function(String?)? validator,
        IconData? prefixIcon,
      }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: AppTheme.textFieldInputDecoration(
        hintText: hintText,
        prefixIconData: prefixIcon,
      ),
      validator: validator ??
              (value) {
            if (value == null || value.trim().isEmpty) {
              return '$hintText cannot be empty';
            }
            return null;
          },
    );
  }

  // Helper for DatePicker styling consistently
  static Widget datePickerTheme(BuildContext context, Widget child) {
    return Theme(
      data: ThemeData.light().copyWith(
        colorScheme: const ColorScheme.light(
          primary: AppTheme.primaryColor, // header background color
          onPrimary: Colors.white, // header text color
          onSurface: AppTheme.textColor, // body text color
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppTheme.primaryColor, // button text color
          ),
        ),
        dialogTheme: DialogThemeData(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.0),
          ),
        ),
      ),
      child: child,
    );
  }

  // Add missing properties needed by dsr_entry.dart
  static Color get primaryTextColor => textColor;

  // Add missing theme properties
  static InputDecorationTheme get inputDecorationTheme => InputDecorationTheme(
    filled: true,
    fillColor: Colors.grey[50],
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.grey[300]!),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: BorderSide(color: Colors.grey[300]!),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: primaryColor, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Colors.red),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  );

  static TextTheme get textTheme => const TextTheme(
    displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: textColor),
    displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: textColor),
    displaySmall: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: textColor),
    headlineLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w600, color: textColor),
    headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: textColor),
    headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: textColor),
    titleLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: textColor),
    titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: textColor),
    titleSmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: textColor),
    bodyLarge: TextStyle(fontSize: 16, color: textColor),
    bodyMedium: TextStyle(fontSize: 14, color: textColor),
    bodySmall: TextStyle(fontSize: 12, color: textColor),
    labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: textColor),
    labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: textColor),
    labelSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w500, color: textColor),
  );

  static BoxDecoration get gradientBackground => const BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFF007AFF),
        Color(0xFF0056CC),
      ],
    ),
  );

  // === CHANGED HERE ===
  static DialogThemeData get dialogTheme => DialogThemeData(
    backgroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16.0),
    ),
  );
  // ====================

  static Widget buildDropdownFormField<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
    required String hintText,
    String? Function(T?)? validator,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      decoration: AppTheme.dropdownDecoration(hintText: hintText),
      validator: validator ??
              (val) => (val == null) ? 'Please make a selection' : null,
      isExpanded: true,
    );
  }
}
