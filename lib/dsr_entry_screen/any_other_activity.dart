import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';

import 'dsr_entry.dart';
import '../theme/app_theme.dart';
import '../models.dart'; // Import your models
import '../api_service.dart'; // Import your ApiService

class AnyOtherActivity extends StatefulWidget {
  const AnyOtherActivity({super.key});

  @override
  State<AnyOtherActivity> createState() => _AnyOtherActivityState();
}

class _AnyOtherActivityState extends State<AnyOtherActivity> {
  final ApiService _apiService = ApiService();
  bool _isSubmitting = false;

  String? _processItem = 'Select';
  final List<String> _processdropdownItems = ['Select', 'Add', 'Update'];

  final TextEditingController _activity1Controller = TextEditingController();
  final TextEditingController _activity2Controller = TextEditingController();
  final TextEditingController _activity3Controller = TextEditingController();
  final TextEditingController _anyOtherPointsController = TextEditingController();

  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _reportDateController = TextEditingController();

  DateTime? _selectedDate;
  DateTime? _selectedReportDate;

  final List<int> _uploadRows = [0]; // Tracks number of rows
  final ImagePicker _picker = ImagePicker();
  final List<File?> _selectedImages = [null]; // Stores selected image files

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Initialize ApiService with actual loginIdM if available
    // _apiService.currentLoginIdM = "your_actual_login_id";

    // Set initial dates in yyyy-MM-dd format
    _selectedDate = DateTime.now();
    _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);
    _selectedReportDate = DateTime.now();
    _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
  }

  @override
  void dispose() {
    _dateController.dispose();
    _reportDateController.dispose();
    _activity1Controller.dispose();
    _activity2Controller.dispose();
    _activity3Controller.dispose();
    _anyOtherPointsController.dispose();
    super.dispose();
  }

  void _addRow() {
    if (_uploadRows.length >= 3) return;
    setState(() {
      _uploadRows.add(_uploadRows.length);
      _selectedImages.add(null);
    });
  }

  void _removeRow(int index) { // Modified to remove specific index
    if (_uploadRows.length <= 1 && _selectedImages.length <=1) return; // Keep at least one
    setState(() {
      // Ensure we are removing a valid index
      if (index < _uploadRows.length && index < _selectedImages.length) {
        _uploadRows.removeAt(index); // This might need rethinking if _uploadRows is just a counter
        _selectedImages.removeAt(index);
        // If _uploadRows was just a counter:
        // _uploadRows.removeLast(); // if you always remove the last one
      }
      // If after removal, it's empty and you always want one row
      if (_uploadRows.isEmpty) {
        _uploadRows.add(0);
        _selectedImages.add(null);
      }
    });
  }

  Future<void> _pickDate(bool isSubmissionDate) async {
    final now = DateTime.now();
    DateTime? initialDate;
    if (isSubmissionDate) {
      initialDate = _selectedDate ?? now;
    } else {
      initialDate = _selectedReportDate ?? now;
    }

    final picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(now.year + 5),
      builder: (context, child) => AppTheme.datePickerTheme(context, child!),
    );
    if (picked != null) {
      setState(() {
        if (isSubmissionDate) {
          _selectedDate = picked;
          _dateController.text = DateFormat('yyyy-MM-dd').format(picked);
        } else {
          _selectedReportDate = picked;
          _reportDateController.text = DateFormat('yyyy-MM-dd').format(picked);
        }
      });
    }
  }

  Future<void> _pickImage(int index) async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        if (index < _selectedImages.length) {
          _selectedImages[index] = File(pickedFile.path);
        } else { // Should not happen if _addRow manages lists correctly
          _selectedImages.add(File(pickedFile.path));
        }
      });
    }
  }

  void _showImageDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
          child: InteractiveViewer(
            panEnabled: false,
            boundaryMargin: const EdgeInsets.all(20),
            minScale: 0.5,
            maxScale: 2,
            child: Image.file(imageFile, fit: BoxFit.contain),
          )
      ),
    );
  }

  Future<String> _getBase64AtIndex(int index) async {
    if (index < _selectedImages.length && _selectedImages[index] != null) {
      final bytes = await _selectedImages[index]!.readAsBytes();
      return base64Encode(bytes);
    }
    return "";
  }

  Future<void> _submitData(bool exitAfter) async {
    if (!_formKey.currentState!.validate()) {
      debugPrint("Form is invalid; skipping submit.");
      return;
    }
    setState(() => _isSubmitting = true);

    final dto = AnyOtherActivityDto(
      proctype: _processItem == 'Select' ? 'Add' : _processItem!,
      submdate: _dateController.text,
      repodate: _reportDateController.text,
      actdetl1: _activity1Controller.text.trim(),
      actdetl2: _activity2Controller.text.trim(),
      actdetl3: _activity3Controller.text.trim(),
      othrnote: _anyOtherPointsController.text.trim(),
      imgfirst: await _getBase64AtIndex(0),
      imgscndd: await _getBase64AtIndex(1),
      imgthird: await _getBase64AtIndex(2),
    );

    try {
      // IMPORTANT: Replace "/specificactivity/anyother" with your actual endpoint
      // OR refactor this DTO and logic to use the main DSR submission with DsrActivityFullDto.
      // Example for DsrActivityFullDto approach:
      // final dsrFullDto = DsrActivityFullDto(
      //   loginIdM: _apiService.currentLoginIdM,
      //   proctype: dto.proctype,
      //   docuDate: dto.submdate, // Assuming submdate is the main DSR date
      //   dsrParam: "XX_ANY_OTHER", // Define a unique param for this activity
      //   otherRemarks: dto.othrnote,
      //   // Map actdetl1,2,3 to dsrRem01,02,03 or custom fields
      //   // Potentially use giftItems or orderItems for images if structure allows,
      //   // or expect backend to handle imgfirst, imgscndd, imgthird based on dsrParam.
      // );
      // await _apiService.submitFullDsr(dsrFullDto);

      await _apiService.submitAnyOtherActivity(dto, "/dsractivity/anyotheractivity"); // Placeholder endpoint

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Submitted successfully."), backgroundColor: Colors.green),
        );
        if (exitAfter) {
          Navigator.of(context).pop();
        } else {
          _resetForm();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Submission failed: $e"), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    setState(() {
      _processItem = 'Select';
      _selectedDate = DateTime.now();
      _selectedReportDate = DateTime.now();
      _dateController.text = DateFormat('yyyy-MM-dd').format(_selectedDate!);
      _reportDateController.text = DateFormat('yyyy-MM-dd').format(_selectedReportDate!);
      _activity1Controller.clear();
      _activity2Controller.clear();
      _activity3Controller.clear();
      _anyOtherPointsController.clear();

      _uploadRows.clear();
      _selectedImages.clear();
      _uploadRows.add(0); // Add first row back
      _selectedImages.add(null); // Add placeholder for first image
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: AppTheme.scaffoldBackgroundColor,
        appBar: AppBar(
          leading: IconButton(
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const DsrEntry()),
              );
            },
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 22),
          ),
          title: Row(
            children: [
              const Icon(Icons.assignment_outlined, size: 28, color: Colors.white),
              const SizedBox(width: 10),
              Text(
                'Any Other Activity',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          backgroundColor: AppTheme.primaryColor,
          elevation: 0,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15),
              bottomRight: Radius.circular(15),
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [AppTheme.scaffoldBackgroundColor, Colors.grey.shade100],
              stops: const [0.0, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  AppTheme.buildSectionCard(
                    title: 'Activity Information',
                    icon: Icons.info_outline,
                    children: [
                      AppTheme.buildLabel('Process Type'),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _processItem,
                        decoration: AppTheme.dropdownDecoration(hintText: 'Select Process'),
                        isExpanded: true,
                        items: _processdropdownItems.map((item) => DropdownMenuItem<String>(
                          value: item,
                          child: Text(item, style: const TextStyle(fontSize: 14)),
                        )).toList(),
                        onChanged: (val) {
                          if (val != null) setState(() => _processItem = val);
                        },
                        validator: (value) => (value == null || value == 'Select') ? 'Please select a process' : null,
                      ),
                      const SizedBox(height: 16),
                      AppTheme.buildSectionCard(
                        title: 'Date Information',
                        icon: Icons.calendar_today,
                        children: [
                          AppTheme.buildLabel('Submission Date'),
                          const SizedBox(height: 8),
                          AppTheme.buildDateField(context, _dateController,() => _pickDate(true),'Select Submission Date', initialDate: _selectedDate),
                          const SizedBox(height: 16),
                          AppTheme.buildLabel('Report Date'),
                          const SizedBox(height: 8),
                          AppTheme.buildDateField(context,_reportDateController,() => _pickDate(false),'Select Report Date', initialDate: _selectedReportDate),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(color: Colors.amber.shade100, borderRadius: BorderRadius.circular(12)),
                              child: Icon(Icons.description_outlined, color: Colors.amber.shade800, size: 24),
                            ),
                            const SizedBox(width: 12),
                            Text('Activity Details', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.amber.shade800)),
                          ],
                        ),
                      ),
                      _buildTextField('Activity Details 1', _activity1Controller),
                      const SizedBox(height: 16),
                      _buildTextField('Activity Details 2', _activity2Controller),
                      const SizedBox(height: 16),
                      _buildTextField('Activity Details 3', _activity3Controller),
                      const SizedBox(height: 16),
                      _buildTextField('Any Other Points', _anyOtherPointsController),
                      const SizedBox(height: 24),

                      Container(
                        margin: const EdgeInsets.only(top: 8, bottom: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: AppTheme.cardDecoration,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(Icons.photo_library_rounded, color: AppTheme.primaryColor, size: 24),
                                const SizedBox(width: 8),
                                Text('Supporting Documents', style: Theme.of(context).textTheme.titleLarge?.copyWith(color: AppTheme.primaryColor)),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text('Upload images related to your activity', style: Theme.of(context).textTheme.bodyMedium),
                            const SizedBox(height: 16),
                            ...List.generate(_uploadRows.length, (index) {
                              return Container(
                                  margin: const EdgeInsets.only(bottom: 16),
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: _selectedImages[index] != null ? Colors.green.shade200 : Colors.grey.shade200,
                                      width: 1.5,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                            decoration: BoxDecoration(color: AppTheme.primaryColor.withOpacity(0.1), borderRadius: BorderRadius.circular(20)),
                                            child: Text('Document ${index + 1}', style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 14)),
                                          ),
                                          const Spacer(),
                                          if (_selectedImages[index] != null)
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                              decoration: BoxDecoration(color: Colors.green.shade100, borderRadius: BorderRadius.circular(20)),
                                              child: const Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(Icons.check_circle, color: Colors.green, size: 16),
                                                  SizedBox(width: 4),
                                                  Text('Uploaded', style: TextStyle(color: Colors.green, fontWeight: FontWeight.w500, fontSize: 14)),
                                                ],
                                              ),
                                            ),
                                        ],
                                      ),
                                      const SizedBox(height:16),
                                      if (_selectedImages[index] != null)
                                        GestureDetector(
                                          onTap: () => _showImageDialog(_selectedImages[index]!),
                                          child: Container(
                                            height: 120, width: double.infinity, margin: const EdgeInsets.only(bottom:16),
                                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), image: DecorationImage(image: FileImage(_selectedImages[index]!), fit: BoxFit.cover)),
                                            child: Align(alignment: Alignment.topRight, child: Container(margin: const EdgeInsets.all(8), padding: const EdgeInsets.all(4), decoration: BoxDecoration(color: Colors.black.withOpacity(0.6), shape: BoxShape.circle), child: const Icon(Icons.zoom_in, color: Colors.white, size: 20))),
                                          ),
                                        ),
                                      Row(children: [
                                        Expanded(child: ElevatedButton.icon(onPressed: () => _pickImage(index), icon: Icon(_selectedImages[index] != null ? Icons.refresh:Icons.upload_file, size:18), label: Text(_selectedImages[index] != null?'Replace':'Upload'), style: AppTheme.elevatedButtonStyle(bgColor: _selectedImages[index] != null ? Colors.amber.shade600 : AppTheme.primaryColor))),
                                        if(_selectedImages[index] != null)...[
                                          const SizedBox(width:8),
                                          Expanded(child: ElevatedButton.icon(onPressed:()=> _showImageDialog(_selectedImages[index]!), icon:const Icon(Icons.visibility, size:18), label:const Text('View'), style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor))),
                                        ]
                                      ],)
                                    ],
                                  )
                              );
                            }),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (_uploadRows.length < 3) // Allow adding only if less than 3
                                  ElevatedButton.icon(onPressed: _addRow, icon: const Icon(Icons.add_photo_alternate, size: 20), label: const Text('Add Document'), style: AppTheme.elevatedButtonStyle()),
                                if (_uploadRows.length > 1) ...[
                                  const SizedBox(width: 12),
                                  ElevatedButton.icon(onPressed: () => _removeRow(_uploadRows.length -1), icon: const Icon(Icons.remove_circle_outline, size: 20), label: const Text('Remove Last'), style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.dangerButtonColor)),
                                ]
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 30),
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: AppTheme.cardDecoration,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Row(children: [const Icon(Icons.save_alt_rounded, color: AppTheme.primaryColor, size:24), const SizedBox(width:8), Text('Submit Your Activity', style:Theme.of(context).textTheme.headlineSmall?.copyWith(color: AppTheme.primaryColor))]), // Adjusted headline
                            const SizedBox(height:4),
                            Text('Save your activity details and continue', style:Theme.of(context).textTheme.bodyMedium),
                            const SizedBox(height:20),
                            ElevatedButton.icon(
                              onPressed: _isSubmitting ? null : () => _submitData(false),
                              icon: const Icon(Icons.save_outlined, size: 20),
                              label: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & New'),
                              style: AppTheme.elevatedButtonStyle(),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton.icon(
                              onPressed: _isSubmitting ? null : () => _submitData(true),
                              icon: const Icon(Icons.check_circle_outline, size: 20),
                              label: _isSubmitting ? const SizedBox(height: 20, width: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 3,)) : const Text('Submit & Exit'),
                              style: AppTheme.elevatedButtonStyle(bgColor: AppTheme.successColor),
                            ),
                            const SizedBox(height: 16),
                            OutlinedButton.icon(
                              onPressed: () { debugPrint('View Submitted Data button pressed'); /* TODO: Implement navigation */ },
                              icon: const Icon(Icons.visibility_outlined, size: 20),
                              label: const Text('View Submitted Data'),
                              style: AppTheme.outlinedButtonStyle(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTheme.buildLabel(label),
        const SizedBox(height: 8),
        Container(
          decoration: AppTheme.textFieldBoxDecoration(),
          child: TextFormField(
            controller: controller,
            maxLines: label.contains("Points") || label.contains("Details") ? 3 : 1,
            style: const TextStyle(fontSize: 16),
            decoration: AppTheme.textFieldInputDecoration(
              hintText: "Enter $label", // More specific hint
              prefixIconData: _getIconForLabel(label),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '$label cannot be empty';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  IconData _getIconForLabel(String label) {
    if (label.contains('Activity Details 1')) return Icons.assignment;
    if (label.contains('Activity Details 2')) return Icons.article;
    if (label.contains('Activity Details 3')) return Icons.description;
    if (label.contains('Any Other Points')) return Icons.lightbulb_outline;
    return Icons.edit_note;
  }
}